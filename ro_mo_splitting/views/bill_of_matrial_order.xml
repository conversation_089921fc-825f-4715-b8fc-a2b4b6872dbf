<odoo>
    <record id="view_bill_of_material_order_list" model="ir.ui.view">
        <field name="name">bill.of.material.order.list</field>
        <field name="model">split.order</field>
        <field name="arch" type="xml">
            <list string="Split Order">
                <field name="name"/>
                <field name="product_id"/>
                <field name="lot_id"/>
                <field name="quantity"/>
                <field name="order_date"/>
            </list>
        </field>
    </record>

    <record id="view_bill_of_material_order_form" model="ir.ui.view">
        <field name="name">bill.of.material.order.form</field>
        <field name="model">split.order</field>
        <field name="arch" type="xml">
            <form string="Split Order">
                <header>
                    <field name="state" readonly="1" widget="statusbar"/>
                    
                    <button name="action_confirm" type="object" 
                    string="Confirm" class="oe_highlight"
                    invisible="state != 'draft'"/>

                    <button name="action_order_create_mo" type="object"
                    string="Create Manufacturing Order" class="oe_highlight"
                    invisible="state != 'confirmed'"/>

                    <button name="action_close" type="object" 
                    string="Close" class="btn-secondary"
                    invisible="state != 'confirmed'"/>

                    <button name="action_cancel" type="object" 
                    string="Cancel" class="btn-danger"
                    invisible="state != 'confirmed'"/>

                </header>

                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_show_manfacture_order" type="object" class="oe_stat_button" icon="fa-wrench">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="mo_counter" readonly="1"/></span>
                                <span class="o_stat_text" readonly="1">Manufactures</span>
                            </div>  
                           
                        </button>
                    </div>
                    <header>
                        <h1><field name="name"/></h1>
                    </header>
                    <group>
                        <group string="Order Details">
                            <field name="product_id" readonly="not can_edit"/>
                            <field name="order_date" readonly="not can_edit"/>
                        </group>
                        <group string="Lot Details">
                            <field name="lot_id" readonly="not can_edit"/>
                            <field name="quantity"/>
                        </group>
                        <notebook>
                            <page string="Product Details">
                                <field name="product_line_ids" widget="one2many_list" readonly="not can_edit">
                                    <list editable="bottom">
                                        <field name="product_id"/>
                                        <field name="product_qty"/>
                                        <!-- <field name="lot_id"/> -->
                                        <!-- <field name="quantity"/> -->
                                    </list>
                                </field>
                                <div style="text-align: right; margin-top: 10px;">
                                    <label for="total_quantity" string="Total Quantity:"/>
                                    <field name="total_quantity" readonly="1" class="oe_inline"/>
                                </div>
                            </page>
                        </notebook>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="split_order_search" model="ir.ui.view">
        <field name="name">split.order.search</field>
        <field name="model">split.order</field>
        <field name="arch" type="xml">
            <search string="Split Order Search">
                <field name="name"/>
                <field name="product_id"/>
                <field name="lot_id"/>
                <field name="quantity"/>

            </search>
        </field>
    </record>
    <record id="action_split_order" model="ir.actions.act_window">
        <field name="name">Split Order</field>
        <field name="res_model">split.order</field>
        <field name="view_mode">list,form,search</field>
    </record>        

    <menuitem id="main_menu_split_order" 
    name="Splitting" 
    parent="mrp.menu_mrp_root" 
    />


    <menuitem id="menu_split_order" 
    name="Split Order" 
    parent="main_menu_split_order" 
    action="action_split_order"/>

</odoo>