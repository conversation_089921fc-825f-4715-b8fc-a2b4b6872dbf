<odoo>
    <record id="view_split_order_line_list" model="ir.ui.view">
        <field name="name">split.order.line.list</field>
        <field name="model">split.order.line</field>
        <field name="arch" type="xml">
            <list string="Split Order Details" create="false" delete="false" >
                <field name="product_id"/>
                <field name="product_qty"/>
                <field name="lot_id"/>
                <field name="split_order_id"/>
                <field name="mrp_production_id"/>
                <field name="state"
                decoration-success="state in ('done', 'to_close')" 
                decoration-warning="state == 'progress'" 
                decoration-info="state == 'confirmed'" 
                decoration-danger="state == 'cancel'" 
                decoration-muted="state == 'draft'" 
                optional="show" widget="badge" class="text-dark"/>
            </list>
        </field>
    </record>
    <record id="view_split_order_line_form" model="ir.ui.view">
        <field name="name">split.order.line.form</field>
        <field name="model">split.order.line</field>
        <field name="arch" type="xml">
            <form string="Split Order Details" create="false" delete="false">
                <sheet>
                    <header>
                        <field name="state" readonly="1" widget="statusbar"/>
                        <h1><field name="product_id" readonly="1"/></h1>
                    </header>
                    <group>
                        <group string="Order Line Details">
                            <field name="product_id" readonly="1"/>
                            <field name="product_qty" readonly="1"/>
                            <field name="lot_id" readonly="1"/>
                        </group>
                        <group string="Refrences">
                            <field name="split_order_id" readonly="1"/>
                            <field name="mrp_production_id" readonly="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_split_order_line_search" model="ir.ui.view">
        <field name="name">split.order.line.search</field>
        <field name="model">split.order.line</field>
        <field name="arch" type="xml">
            <search string="Search Order Details">
                <field name="product_id"/>
                <field name="product_qty"/>
                <field name="lot_id"/>
                <field name="split_order_id"/>
                <field name="mrp_production_id"/>
                <field name="state" string="Status" filter_domain="[('state', '=', 'state')]"/>
                <filter name="date" string="Date"  context="{'group_by': 'date'}"/>
                <group string="Filter by Status" >
                    <filter name="confirmed" string="Confirmed" domain="[('state', '=', 'confirmed')]"/>
                    <filter name="done" string="Done" domain="[('state', '=', 'done')]"/>
                    <filter name="draft" string="Draft" domain="[('state', '=', 'draft')]"/>
                    <filter name="cancel" string="Cancelled" domain="[('state', '=', 'cancel')]"/>
                </group>
                <group expand="0" string="Group By">
                    <filter name="product_id" string="Product ID" domain="[('product_id', '=', 'product_id')]" context="{'group_by': 'product_id'}"/>
                    <filter name="lot_id" string="Lot ID" domain="[('lot_id', '=', 'lot_id')]" context="{'group_by': 'lot_id'}"/>
                    <filter name="state" string="Status" domain="[('state', '=', 'state')]" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>
    <record id="action_bill_of_material_order_line" model="ir.actions.act_window">
        <field name="name">Split Order Details</field>
        <field name="res_model">split.order.line</field>
            <field name="domain">[('split_order_id.state', 'in', ['confirmed', 'close'])]</field>
        <field name="view_mode">list,form,search</field>
    </record>        

    <menuitem id="menu_split_order_line" 
    name="Split Order Details" 
    parent="main_menu_split_order" 
    action="action_split_order_line"/>

</odoo>