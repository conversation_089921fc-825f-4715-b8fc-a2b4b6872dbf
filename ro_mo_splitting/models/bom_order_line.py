from odoo import models, fields, api
from odoo.exceptions import UserError, ValidationError


class SplitOrderLine(models.Model):
    _name = 'split.order.line'
    _description = 'Split Order Line'
    _order = 'date desc'

    name = fields.Char(string='Name', readonly=True, compute='_compute_name', store=True)
    split_order_id = fields.Many2one('split.order', string='Split Order', required=True)
    product_id = fields.Many2one('product.product', string='Finished Product', required=True)
    product_qty = fields.Float(string='Quantity', required=True)
    lot_id = fields.Many2one(related='split_order_id.lot_id', string='Lot/Serial', store=True)
    mrp_production_id = fields.Many2one('mrp.production', string='Manufacturing Order', copy=False)
    date = fields.Date(related='split_order_id.order_date', string='Order Date', store=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('progress', 'In Progress'),
        ('confirmed', 'Confirmed'),
        ('done', 'Done'),
        ('cancel', 'Cancelled'),
        ('to_close', 'To Close'),
    ], string='Status', default='draft'
    , compute='_compute_state', store=True)

    def _compute_name(self):
        for line in self:
            if line.product_id:
                line.name = f"{line.product_id.display_name} - {line.product_qty}"
            else:
                line.name = "No Product"
    
    @api.depends('mrp_production_id.state')
    def _compute_state(self):
        for line in self:
            if line.mrp_production_id.state:
                line.state = line.mrp_production_id.state
            else:
                line.state = 'draft'     

    def action_create_mo(self):
        for line in self:
            if line.mrp_production_id:
                raise UserError("A Manufacturing Order already exists for this line.")

            if not line.split_order_id.product_id or not line.split_order_id.lot_id:
                raise UserError("Please set both the raw material and lot/serial number on the Split order.")

            mo = self.env['mrp.production'].create({
                'product_id': line.product_id.id,
                'product_qty': line.product_qty,
                'product_uom_id': line.product_id.uom_id.id,
                'fp_lot_id': str(line.lot_id.name),
            })
            mo.action_confirm()

            quant = self.env['stock.quant'].search([
                ('product_id', '=', line.split_order_id.product_id.id),
                ('lot_id', '=', line.split_order_id.lot_id.id),
                ('location_id.usage', '=', 'internal'),
                ('quantity', '>', 0),
            ], limit=1)

            required_qty = line.product_qty
            available_qty = quant.quantity - quant.reserved_quantity if quant else 0
            if available_qty < required_qty:
                raise UserError(
                    f"Not enough stock for raw material '{line.split_order_id.product_id.display_name}' "
                    f"with lot/serial number '{line.split_order_id.lot_id.name}'. "
                    f"Required: {required_qty}, Available: {available_qty}."
                )

            new_move = self.env['stock.move'].create({
                'name': line.split_order_id.product_id.display_name,
                'product_id': line.split_order_id.product_id.id,
                'product_uom_qty': line.product_qty,
                'product_uom': line.split_order_id.product_id.uom_id.id,
                'production_id': mo.id,
                'location_id': mo.location_src_id.id,
                'location_dest_id': mo.location_dest_id.id,
                'move_line_ids': [(0, 0, {
                    'product_id': line.split_order_id.product_id.id,
                    'qty_done': line.product_qty, 
                    'product_uom_id': line.split_order_id.product_id.uom_id.id,
                    'lot_id': line.split_order_id.lot_id.id,
                    'location_id': mo.location_src_id.id,
                    'location_dest_id': mo.location_dest_id.id,
                })],
            })

            mo.write({
                'move_raw_ids': [(4, new_move.id)],
            })

            line.mrp_production_id = mo.id
 
