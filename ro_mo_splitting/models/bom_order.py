from odoo import models, fields, api
from odoo.exceptions import UserError, ValidationError

class SplitOrder(models.Model):
    _name = 'split.order'
    _description = 'Split Order'
    _order = 'order_date desc'

    name = fields.Char(string='Order Reference', 
                       copy=False, 
                       store=True,
                       readonly=True, default='New')
    quantity = fields.Float(related='lot_id.product_qty', string='Quantity', readonly=True,store=True)
    order_date = fields.Date(string='Order Date', required=True, default=fields.Date.today())

    product_id = fields.Many2one('product.product', string='Raw Material', required=True)
    lot_id = fields.Many2one('stock.lot', string='Lot/Serial', required=True
                             ,domain="[('product_id', '=', product_id)]"
                             ,context={'no_create': True, 'no_edit': True})
    product_line_ids = fields.One2many('split.order.line', 'split_order_id', string='Products')

    total_quantity = fields.Float(string="Total Quantity", compute="_compute_total_quantity", store=True)
    mrp_production_id = fields.Many2one('mrp.production', string='Manufacturing Order', copy=False)
    mo_counter = fields.Integer(string='Manufacturing Order Counter',compute='_compute_mo_counter')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('cancel', 'Cancelled'),
        ('confirmed', 'Confirmed'),
        ('close', 'Close'),

    ], string='Status', default='draft')

    can_edit = fields.Boolean(compute='_compute_can_edit', store=False)

    @api.depends('state')
    def _compute_can_edit(self):
        for record in self:
            if record.state in ['close', 'cancel']:
                record.can_edit = False
            elif record.state == 'confirmed':
                is_manufacture_admin = self.env.user.has_group('mrp.group_mrp_manager')
                record.can_edit = is_manufacture_admin
            else:  # draft
                record.can_edit = True

    @api.model
    def create(self,vals):
        vals['name'] = self.env['ir.sequence'].next_by_code('split.order')
        return super().create(vals)

    @api.depends('product_line_ids.product_qty')
    def _compute_total_quantity(self):
        for record in self:
            #restriction for sum not to exceed the quantity of the lot
            if record.lot_id and record.lot_id.product_qty < sum(line.product_qty for line in record.product_line_ids):
                raise ValidationError("Total quantity of products cannot exceed the quantity of the selected lot.")
            record.total_quantity = sum(line.product_qty for line in record.product_line_ids)
    
    @api.onchange('product_id')
    def _onchange_product_id(self):
        self.lot_id = False # Reset lot_id when product_id changes


    def action_confirm(self):
        for record in self:
            if record.state != 'draft':
                raise UserError("Only draft orders can be confirmed.")
            record.state = 'confirmed'

    def action_close(self):
        for record in self:
            if record.state != 'confirmed':
                raise UserError("Only confirmed orders can be closed.")
            record.state = 'close'

    def action_cancel(self):
        for record in self:
            if record.state != 'confirmed':
                raise UserError("Only confirmed orders can be canceled.")
            record.state = 'cancel'


    @api.depends('product_line_ids')
    def action_order_create_mo(self):
        for record in self:
            for line in record.product_line_ids:
                if line.state != 'draft':
                    raise UserError("Only draft lines can create manufacturing orders.")
                line.action_create_mo()
    def _compute_mo_counter(self):
        for record in self:
            record.mo_counter = 0
            for line in record.product_line_ids:
                if line.mrp_production_id:
                    record.mo_counter += 1
                

    #shows the manfacture orders generaterd from the Split order
    def action_show_manfacture_order(self):
        self.ensure_one()
        if not self.product_line_ids.mrp_production_id:
            raise UserError("No Manufacturing Order found for this Split order.")
        return {
            'name': 'Manfacture Orders',
            'view_mode': 'list,form',
            'res_model':'mrp.production',
            'type': 'ir.actions.act_window',
            'domain': [('id', 'in', self.product_line_ids.mrp_production_id.ids)],
        }
        


