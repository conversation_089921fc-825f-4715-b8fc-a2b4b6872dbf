<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_get_notifications" model="ir.cron">
        <field name="name">ETA Get Notifications</field>
        <field name="interval_number">15</field>
        <field name="interval_type">minutes</field>
        <!-- <field name="numbercall">-1</field> -->
        <field name="nextcall" eval="(DateTime.now().replace(hour=0, minute=0)).strftime('%Y-%m-%d %H:%M:%S')" />
        <!-- <field name="doall" eval="False"/> -->
        <field name="priority">0</field>
        <field name="model_id" ref="model_eta_manage"/>
        <field name="code">model.get_eta_notifications('en')</field>
        <field name="state">code</field>
        <field name="active" eval="False"/>
    </record>
</odoo>
