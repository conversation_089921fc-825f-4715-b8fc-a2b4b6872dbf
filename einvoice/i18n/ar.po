# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* einvoice
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-06 23:50+0000\n"
"PO-Revision-Date: 2022-05-06 23:50+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_document_type_version__0_9
msgid "0.9"
msgstr ""

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_document_type_version__1_0
msgid "1.0"
msgstr ""

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_company_branches_form
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
msgid "17"
msgstr ""

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_company_branches_form
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
msgid "17 Nabil Al Wakad"
msgstr ""

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__accepted
msgid "Accepted"
msgstr "مقبول"

#. module: einvoice
#: model:ir.model,name:einvoice.model_account_move_reversal
msgid "Account Move Reversal"
msgstr "الحركة العكسية"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_additionalInformation
msgid "Additional Information"
msgstr "معلومات اخري"

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_company_branches_form
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
msgid "Address"
msgstr "العنوان"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_tax__eta_arabic_description
msgid "Arabic Description"
msgstr "الوصف بالعربي"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_branchId
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_branchId
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_branchId
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_branchId
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_branchId
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_branchId
msgid "Branch Id"
msgstr "الفرع"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_building_number
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_building_number
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_building_number
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_building_number
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_building_number
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_building_number
msgid "Building Number"
msgstr "رقم المبني"

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
msgid "Building Number..."
msgstr "رقم المبني..."

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__res_partner__eta_type__b
msgid "Business In Egypt"
msgstr "شركة في مصر"

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.cancel_account_move_view
msgid "Cacnel Document"
msgstr "رفض الفاتورة"

#. module: einvoice
#: code:addons/einvoice/models/move.py:0
#, python-format
msgid "Can't edit valid document cancel first."
msgstr "لا يمكن تحرير مستند صالح إلغاء أولاً."

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.cancel_account_move_view
msgid "Cancel"
msgstr "إلغاء"

#. module: einvoice
#: model:ir.model,name:einvoice.model_cancel_account_move
msgid "Cancel Account Move"
msgstr "إلغاء حركة الحساب"

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_form_eta
msgid "Cancel Entry"
msgstr "إلغاء القيد"

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_form_eta
msgid "Cancel Entry (ETA)"
msgstr "إالغاء القيد من الضرائب"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_cancel_reason
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_cancel_reason
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_cancel_reason
#: model:ir.model.fields,field_description:einvoice.field_cancel_account_move__eta_cancel_reason
msgid "Cancel Reason"
msgstr "سبب الرفض"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__canceled
msgid "Canceled"
msgstr "ملغي"

#. module: einvoice
#: code:addons/einvoice/models/eta.py:0
#, python-format
msgid "Check Later"
msgstr "تحقق لاحقا"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_tax__eta_code
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types__code
#: model:ir.model.fields,field_description:einvoice.field_uom_uom__eta_code
msgid "Code"
msgstr "الكود"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_product_product__eta_code_type
#: model:ir.model.fields,field_description:einvoice.field_product_template__eta_code_type
msgid "Code Type"
msgstr "نوع الكود"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_product_product__eta_item_code
#: model:ir.model.fields,help:einvoice.field_product_template__eta_item_code
msgid ""
"Code of the goods or services item being sold. GS1 codes targeted for "
"managing goods, EGS codes targeted for managing goods – goods or "
"services.[********]"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_product_product__eta_code_type
#: model:ir.model.fields,help:einvoice.field_product_template__eta_code_type
msgid ""
"Coding schema used to encode the code type. Must be GS1 or EGS for this "
"version.[GS1]"
msgstr ""

#. module: einvoice
#: model:ir.model,name:einvoice.model_res_company
msgid "Companies"
msgstr "شركات"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types__company_id
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__company_id
msgid "Company"
msgstr "شركة"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_company_branch_id
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_company_branch_id
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_company_branch_id
#: model:ir.model.fields,field_description:einvoice.field_crm_team__eta_company_branch_id
msgid "Company Branch"
msgstr "فرع الشركة"

#. module: einvoice
#: model:ir.actions.act_window,name:einvoice.action_eta_comp_bran
#: model:ir.ui.menu,name:einvoice.menu_eta_company_branches
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_company_branches_form
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_company_branches_tree
msgid "Company Branches"
msgstr "فروع الشركة"

#. module: einvoice
#: model:ir.model,name:einvoice.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_customer_country_id
#: model:ir.model.fields,help:einvoice.field_account_move__eta_customer_country_id
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_customer_country_id
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_country_id
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_country_id
#: model:ir.model.fields,help:einvoice.field_res_users__eta_country_id
msgid ""
"Country         represented by ISO-3166-2 2 symbol code of the countries."
"             Must be EG for internal business issuers.[EG]"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_cancel_account_move__create_uid
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types__create_uid
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__create_uid
#: model:ir.model.fields,field_description:einvoice.field_eta_manage__create_uid
msgid "Created by"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_cancel_account_move__create_date
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types__create_date
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__create_date
#: model:ir.model.fields,field_description:einvoice.field_eta_manage__create_date
msgid "Created on"
msgstr ""

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_document_type__c
msgid "Credit Note"
msgstr "اشعار دائن"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_date_time_issued
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_date_time_issued
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_date_time_issued
msgid "Date Time Issued"
msgstr "تاريخ الاعتماد"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_document_type__d
msgid "Debit Note"
msgstr "اشعار مدين"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_move__display_name
#: model:ir.model.fields,field_description:einvoice.field_account_move_reversal__display_name
#: model:ir.model.fields,field_description:einvoice.field_account_tax__display_name
#: model:ir.model.fields,field_description:einvoice.field_cancel_account_move__display_name
#: model:ir.model.fields,field_description:einvoice.field_crm_team__display_name
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types__display_name
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__display_name
#: model:ir.model.fields,field_description:einvoice.field_eta_manage__display_name
#: model:ir.model.fields,field_description:einvoice.field_product_product__display_name
#: model:ir.model.fields,field_description:einvoice.field_product_template__display_name
#: model:ir.model.fields,field_description:einvoice.field_res_company__display_name
#: model:ir.model.fields,field_description:einvoice.field_res_partner__display_name
#: model:ir.model.fields,field_description:einvoice.field_uom_uom__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_document_type
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_document_type
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_document_type
msgid "Document Type"
msgstr "نوع الفاتورة"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_document_type
#: model:ir.model.fields,help:einvoice.field_account_move__eta_document_type
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_document_type
msgid "Document type name.[i]"
msgstr ""

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_company_branches_form
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
msgid "Dokki"
msgstr ""

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__draft
msgid "Draft"
msgstr "مسودة"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__product_product__eta_code_type__egs
#: model:ir.model.fields.selection,name:einvoice.selection__product_template__eta_code_type__egs
msgid "EGS"
msgstr ""

#. module: einvoice
#: model:ir.ui.menu,name:einvoice.eta_main_menu
#: model_terms:ir.ui.view,arch_db:einvoice.crm_team_view_form_eta
#: model_terms:ir.ui.view,arch_db:einvoice.product_template_product_template_form_view_eta
#: model_terms:ir.ui.view,arch_db:einvoice.product_uom_form_view_eta
#: model_terms:ir.ui.view,arch_db:einvoice.view_company_form_eta
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_form_eta
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
#: model_terms:ir.ui.view,arch_db:einvoice.view_tax_form_eta
msgid "ETA"
msgstr ""

#. module: einvoice
#: model:ir.model,name:einvoice.model_eta_activity_types
msgid "ETA Activity Types"
msgstr "انواع النشاط ETA"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_additionalInformation
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_additionalInformation
msgid "ETA Additional Information"
msgstr "معلومات أخري"

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.cancel_account_move_view
msgid "ETA Cancel"
msgstr "رفض ETA"

#. module: einvoice
#: model:ir.actions.act_window,name:einvoice.action_move_cancel_eta_button
msgid "ETA Cancel Document"
msgstr "رفض الفاتورة ETA"

#. module: einvoice
#: model:ir.model,name:einvoice.model_eta_company_branch
msgid "ETA Company Branch"
msgstr "فرع الشركة"

#. module: einvoice
#: model:ir.actions.server,name:einvoice.ir_cron_get_notifications_ir_actions_server
#: model:ir.cron,cron_name:einvoice.ir_cron_get_notifications
#: model:ir.cron,name:einvoice.ir_cron_get_notifications
msgid "ETA Get Notifications"
msgstr "استلام الاشعارات"

#. module: einvoice
#: model:ir.actions.server,name:einvoice.move_get_eta_state
msgid "ETA Get State & PDF"
msgstr "الحالة والوثيقة ETA"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_governate
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_governate
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_governate
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_governate
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_governate
msgid "ETA Governate"
msgstr "المحافظة"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_landmark
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_landmark
msgid "ETA Landmark"
msgstr "علامة ميزة"

#. module: einvoice
#: model:ir.model,name:einvoice.model_eta_manage
msgid "ETA Manage"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_name
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_name
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_name
#: model:ir.model.fields,field_description:einvoice.field_res_company__eta_name
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_name
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_name
msgid "ETA Name"
msgstr "الاسم"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_region_city
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_region_city
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_region_city
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_region_city
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_region_city
msgid "ETA Region City"
msgstr "المدينة"

#. module: einvoice
#: model:ir.actions.server,name:einvoice.move_send_eta
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_form_eta
msgid "ETA Send"
msgstr "إرسال ETA"

#. module: einvoice
#: model:ir.actions.server,name:einvoice.move_sign_eta
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_form_eta
msgid "ETA Sign"
msgstr "إمضاء ETA"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_state
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_state
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_state
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_search_eta
msgid "ETA Status"
msgstr "حالة ETA"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_street
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_street
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_street
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_street
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_street
msgid "ETA Street"
msgstr "الشارع"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_tax__eta_english_description
#: model:ir.model.fields,field_description:einvoice.field_uom_uom__eta_english_description
msgid "English Description"
msgstr "الوصف الإنجليزي"

#. module: einvoice
#: code:addons/einvoice/models/eta.py:0 code:addons/einvoice/models/eta.py:0
#: code:addons/einvoice/models/eta.py:0
#, python-format
msgid "Error %s %s"
msgstr ""

#. module: einvoice
#: code:addons/einvoice/models/eta.py:0
#, python-format
msgid "Error %s description %s uri %s"
msgstr ""

#. module: einvoice
#: code:addons/einvoice/models/eta.py:0 code:addons/einvoice/models/eta.py:0
#, python-format
msgid "Fields are required {}"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_floor
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_floor
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_floor
msgid "Floor"
msgstr "الطابق"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__res_partner__eta_type__f
msgid "Foreigner"
msgstr "اجنبي"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__product_product__eta_code_type__gs1
#: model:ir.model.fields.selection,name:einvoice.selection__product_template__eta_code_type__gs1
msgid "GS1"
msgstr ""

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_form_eta
msgid "Get State & PDF"
msgstr "جلب الحالة والوثيقة"

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_company_branches_form
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
msgid "Giza Governorate"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_governate
msgid "Governate"
msgstr "محافظة"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_customer_governate
#: model:ir.model.fields,help:einvoice.field_account_move__eta_customer_governate
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_customer_governate
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_governate
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_governate
#: model:ir.model.fields,help:einvoice.field_res_users__eta_governate
msgid "Governorate information as textual value.[Giza Governorate]"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_move__id
#: model:ir.model.fields,field_description:einvoice.field_account_move_reversal__id
#: model:ir.model.fields,field_description:einvoice.field_account_tax__id
#: model:ir.model.fields,field_description:einvoice.field_cancel_account_move__id
#: model:ir.model.fields,field_description:einvoice.field_crm_team__id
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types__id
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__id
#: model:ir.model.fields,field_description:einvoice.field_eta_manage__id
#: model:ir.model.fields,field_description:einvoice.field_product_product__id
#: model:ir.model.fields,field_description:einvoice.field_product_template__id
#: model:ir.model.fields,field_description:einvoice.field_res_company__id
#: model:ir.model.fields,field_description:einvoice.field_res_partner__id
#: model:ir.model.fields,field_description:einvoice.field_uom_uom__id
msgid "ID"
msgstr "المُعرف"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__invalid
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_search_eta
msgid "Invalid"
msgstr "غير صحيح"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_invalid_reason
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_invalid_reason
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_invalid_reason
msgid "Invalid Reason"
msgstr "سبب عدم الصحة"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_document_type__i
msgid "Invoice"
msgstr "فاتورة"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_invoice_uuid
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_invoice_uuid
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_invoice_uuid
msgid "Invoice UUID"
msgstr "معرف الفاتورة"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_country_id
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_country_id
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_country_id
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_country_id
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_country_id
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_country_id
msgid "Issuer Country"
msgstr "الدولة"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_type
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_type
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_type
#: model:ir.model.fields,field_description:einvoice.field_res_company__eta_type
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_type
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_type
msgid "Issuer Type"
msgstr "النوع"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_product_product__eta_item_code
#: model:ir.model.fields,field_description:einvoice.field_product_template__eta_item_code
msgid "Item Code"
msgstr "كود الصنف"

#. module: einvoice
#: model:ir.model,name:einvoice.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_json_text
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_json_text
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_json_text
msgid "Json Text"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_landmark
msgid "Landmark"
msgstr "علامة مميزة"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_move____last_update
#: model:ir.model.fields,field_description:einvoice.field_account_move_reversal____last_update
#: model:ir.model.fields,field_description:einvoice.field_account_tax____last_update
#: model:ir.model.fields,field_description:einvoice.field_cancel_account_move____last_update
#: model:ir.model.fields,field_description:einvoice.field_crm_team____last_update
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types____last_update
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch____last_update
#: model:ir.model.fields,field_description:einvoice.field_eta_manage____last_update
#: model:ir.model.fields,field_description:einvoice.field_product_product____last_update
#: model:ir.model.fields,field_description:einvoice.field_product_template____last_update
#: model:ir.model.fields,field_description:einvoice.field_res_company____last_update
#: model:ir.model.fields,field_description:einvoice.field_res_partner____last_update
#: model:ir.model.fields,field_description:einvoice.field_uom_uom____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_cancel_account_move__write_uid
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types__write_uid
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__write_uid
#: model:ir.model.fields,field_description:einvoice.field_eta_manage__write_uid
msgid "Last Updated by"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_cancel_account_move__write_date
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types__write_date
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__write_date
#: model:ir.model.fields,field_description:einvoice.field_eta_manage__write_date
msgid "Last Updated on"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_company_branch_id
#: model:ir.model.fields,help:einvoice.field_account_move__eta_company_branch_id
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_company_branch_id
#: model:ir.model.fields,help:einvoice.field_crm_team__eta_company_branch_id
msgid ""
"Mandatory when issuer is of type B,             otherwise optional. The code"
" of the branch as registered with tax authority for the             company "
"submitting the document.[1234]"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_customer_branchId
#: model:ir.model.fields,help:einvoice.field_account_move__eta_customer_branchId
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_customer_branchId
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_branchId
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_branchId
#: model:ir.model.fields,help:einvoice.field_res_users__eta_branchId
msgid ""
"Mandatory when issuer is of type B,         otherwise optional. The code of "
"the branch as registered with tax authority for the             company "
"submitting the document.[1234]"
msgstr ""

#. module: einvoice
#: code:addons/einvoice/wizard/move_cancel_account.py:0
#, python-format
msgid "Missing 'active_model' in context."
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_doc_pdf_name
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_doc_pdf_name
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_doc_pdf_name
#: model:ir.model.fields,field_description:einvoice.field_eta_activity_types__name
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__name
msgid "Name"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_passport_national_id
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_passport_national_id
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_passport_national_id
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_passport_national_id
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_passport_national_id
msgid "National/Passport Id"
msgstr "الرقم القومي / جواز السفر "

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__res_partner__eta_type__p
msgid "Natural Person"
msgstr "شخص عادي"

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_company_branches_form
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
msgid "Optional Fields"
msgstr "حقول اختيارية"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_postalCode
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_postalCode
#: model:ir.model.fields,help:einvoice.field_res_users__eta_postalCode
msgid "Optional: Postal code"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_additionalInformation
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_additionalInformation
#: model:ir.model.fields,help:einvoice.field_res_users__eta_additionalInformation
msgid "Optional: any additional information to the address"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_landmark
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_landmark
#: model:ir.model.fields,help:einvoice.field_res_users__eta_landmark
msgid "Optional: nearest landmark to the address"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_floor
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_floor
#: model:ir.model.fields,help:einvoice.field_res_users__eta_floor
msgid "Optional: the floor number"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_room
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_room
#: model:ir.model.fields,help:einvoice.field_res_users__eta_room
msgid "Optional: the room/flat number in the floor"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_doc_pdf_ar
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_doc_pdf_ar
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_doc_pdf_ar
msgid "PDF Arabic"
msgstr "الفاتورة بالعربي"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_doc_pdf
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_doc_pdf
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_doc_pdf
msgid "PDF English"
msgstr "الفاتورة بالانجليزي"

#. module: einvoice
#: code:addons/einvoice/models/move.py:0
#, python-format
msgid "Please get eta state first."
msgstr "برجاء الحصول علي الحالة اولا"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_postalCode
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_postalCode
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_postalCode
msgid "Postal Code"
msgstr "الرمز البريدي"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_is_preproduction
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_is_preproduction
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_is_preproduction
msgid "Preproduction"
msgstr "قبل الفعلي"

#. module: einvoice
#: model:ir.model,name:einvoice.model_product_product
msgid "Product"
msgstr "المنتج"

#. module: einvoice
#: model:ir.model,name:einvoice.model_product_template
msgid "Product Template"
msgstr "قالب المنتج"

#. module: einvoice
#: model:ir.model,name:einvoice.model_uom_uom
msgid "Product Unit of Measure"
msgstr "وحدة قياس المنتج"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_region_city
msgid "Region City"
msgstr "المدينة"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_customer_region_city
#: model:ir.model.fields,help:einvoice.field_account_move__eta_customer_region_city
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_customer_region_city
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_region_city
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_region_city
#: model:ir.model.fields,help:einvoice.field_res_users__eta_region_city
msgid "Region and city information as textual value.[Dokki]"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_customer_id
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_customer_id
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_customer_id
#: model:ir.model.fields,field_description:einvoice.field_res_company__eta_id
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_id
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_id
msgid "Registration Number"
msgstr "المعرف الضريبي"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_customer_name
#: model:ir.model.fields,help:einvoice.field_account_move__eta_customer_name
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_customer_name
#: model:ir.model.fields,help:einvoice.field_res_company__eta_name
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_name
#: model:ir.model.fields,help:einvoice.field_res_users__eta_name
msgid "Registration name of the company.[My company]"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_customer_id
#: model:ir.model.fields,help:einvoice.field_account_move__eta_customer_id
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_customer_id
#: model:ir.model.fields,help:einvoice.field_res_company__eta_id
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_id
#: model:ir.model.fields,help:einvoice.field_res_users__eta_id
msgid ""
"Registration number. For business in Egypt must be registration "
"number.[***********]"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_reject_reason
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_reject_reason
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_reject_reason
msgid "Reject Reason"
msgstr "سبب الرفض"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__rejected
msgid "Rejected"
msgstr "مرفوض"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_room
#: model:ir.model.fields,field_description:einvoice.field_res_partner__eta_room
#: model:ir.model.fields,field_description:einvoice.field_res_users__eta_room
msgid "Room"
msgstr "الغرفة"

#. module: einvoice
#: model:ir.model,name:einvoice.model_crm_team
msgid "Sales Team"
msgstr "فريق المبيعات"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_sign
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_sign
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_sign
msgid "Sign Text"
msgstr "الامضاء"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__signed
msgid "Signed"
msgstr "تم الإمضاء"

#. module: einvoice
#: code:addons/einvoice/models/eta.py:0
#, python-format
msgid "Something Wrong"
msgstr "هناك شئ خاطئ"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_eta_company_branch__eta_street
msgid "Street"
msgstr "الشارع"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__submitted
msgid "Submitted"
msgstr "تم الارسال"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_tax__eta_sub_arabic_description
msgid "Subtype Arabic Description"
msgstr "النوع الفرعي بالعربي"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_tax__eta_sub_code
msgid "Subtype Code"
msgstr "كود النوع الفرعي"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_tax__eta_sub_english_description
msgid "Subtype English Description"
msgstr "النوع الفرعي بالانجليزية"

#. module: einvoice
#: model:ir.model,name:einvoice.model_account_tax
msgid "Tax"
msgstr "الضريبة"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_taxpayer_activity_code_id
#: model:ir.model.fields,help:einvoice.field_account_move__eta_taxpayer_activity_code_id
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_taxpayer_activity_code_id
msgid ""
"Tax activity code of the business issuing the document             "
"representing the activity that caused it to be issued.[9478]"
msgstr ""

#. module: einvoice
#: model:ir.actions.act_window,name:einvoice.action_eta_tax_type
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_taxpayer_activity_code_id
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_taxpayer_activity_code_id
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_taxpayer_activity_code_id
#: model:ir.ui.menu,name:einvoice.menu_eta_activity_types
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_activity_types_form
#: model_terms:ir.ui.view,arch_db:einvoice.view_eta_activity_types_tree
msgid "Taxpayer Activity Code"
msgstr "رمز نشاط دافع الضرائب"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_date_time_issued
#: model:ir.model.fields,help:einvoice.field_account_move__eta_date_time_issued
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_date_time_issued
msgid ""
"The date and time when the document was issued."
"                                              Date and time cannot be in "
"future.                                             Time to be supplied in "
"UTC timezone.[2015-02-13T13:15:00Z]"
msgstr ""

#. module: einvoice
#: code:addons/einvoice/wizard/move_cancel_account.py:0
#, python-format
msgid "There are no journal items in the draft state to post."
msgstr "لا توجد عناصر دفتر يومية في حالة المسودة لنشرها."

#. module: einvoice
#: code:addons/einvoice/models/eta.py:0
#, python-format
msgid "Time should be in UTC and not future."
msgstr ""

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_search_eta
msgid "To Sign"
msgstr "للإمضاء"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__tosign
msgid "ToSign"
msgstr "للإمضاء"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_document_type_version
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_document_type_version
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_document_type_version
msgid "Type Version"
msgstr "اصدار النوع"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_customer_type
#: model:ir.model.fields,help:einvoice.field_account_move__eta_customer_type
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_customer_type
#: model:ir.model.fields,help:einvoice.field_res_company__eta_type
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_type
#: model:ir.model.fields,help:einvoice.field_res_users__eta_type
msgid ""
"Type of the issuer - supported values - B for business in Egypt,"
"                     P for natural person, F for foreigner."
"                         Note that P and F are reserved values for future "
"use.[B]"
msgstr ""

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_account_bank_statement_line__eta_uuid
#: model:ir.model.fields,field_description:einvoice.field_account_move__eta_uuid
#: model:ir.model.fields,field_description:einvoice.field_account_payment__eta_uuid
msgid "UUID"
msgstr "معرف"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__valid
#: model_terms:ir.ui.view,arch_db:einvoice.view_move_search_eta
msgid "Valid"
msgstr "صحيح"

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_customer_building_number
#: model:ir.model.fields,help:einvoice.field_account_move__eta_customer_building_number
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_customer_building_number
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_building_number
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_building_number
#: model:ir.model.fields,help:einvoice.field_res_users__eta_building_number
msgid "building information.[17]"
msgstr "بناء رقم"

#. module: einvoice
#: model:ir.model.fields,field_description:einvoice.field_res_company__date_from_notifications
msgid "date_from_notifications"
msgstr ""

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
msgid "e.g. **************"
msgstr ""

#. module: einvoice
#: model_terms:ir.ui.view,arch_db:einvoice.view_partner_form_eta
msgid "e.g. BE0477472701"
msgstr ""

#. module: einvoice
#: code:addons/einvoice/models/eta.py:0
#, python-format
msgid "product internal reference is required on preproduction."
msgstr ""

#. module: einvoice
#: model:ir.model.fields,help:einvoice.field_account_bank_statement_line__eta_customer_street
#: model:ir.model.fields,help:einvoice.field_account_move__eta_customer_street
#: model:ir.model.fields,help:einvoice.field_account_payment__eta_customer_street
#: model:ir.model.fields,help:einvoice.field_eta_company_branch__eta_street
#: model:ir.model.fields,help:einvoice.field_res_partner__eta_street
#: model:ir.model.fields,help:einvoice.field_res_users__eta_street
msgid "street information.[17 Nabil Al Wakad]"
msgstr "الشارع"

#. module: einvoice
#: model:ir.model.fields.selection,name:einvoice.selection__account_move__eta_state__tocancel
msgid "tocancel"
msgstr "للرفض"