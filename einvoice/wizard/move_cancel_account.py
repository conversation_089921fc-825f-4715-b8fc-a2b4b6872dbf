from odoo import models, fields, _
from odoo.exceptions import UserError


class CancelAccountMove(models.TransientModel):
    _name = "cancel.account.move"
    _description = "Cancel Account Move"

    eta_cancel_reason = fields.Text(
        string='Cancel Reason',
        copy=False
    )

    def cancel_move(self):
        if self._context.get('active_model') == 'account.move':
            ids = self._context.get('active_ids', [])
            domain = [('id', 'in', ids), ('eta_state', '=', 'valid')]
        else:
            raise UserError(_("Missing 'active_model' in context."))

        moves = self.env['account.move'].search(domain)
        if not moves:
            raise UserError(_('There are no journal items in the draft state to post.'))

        moves.write({'eta_cancel_reason':self.eta_cancel_reason})

        moves.action_invoice_cancel_document()

        return {'type': 'ir.actions.act_window_close'}
