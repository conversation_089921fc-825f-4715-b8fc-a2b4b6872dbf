<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--Account Move lines-->
        <record id="cancel_account_move_view" model="ir.ui.view">
            <field name="name">ETA Cancel Document</field>
            <field name="model">cancel.account.move</field>
            <field name="arch" type="xml">
                <form string="ETA Cancel">
                    <group>
                        <field name="eta_cancel_reason"/>
                    </group>
                    <footer>
                        <button string="Cacnel Document" name="cancel_move" type="object" default_focus="1" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
        <!--Open wizard from cancel button in account move form view-->
        <record model="ir.actions.act_window" id="action_move_cancel_eta_button">
            <field name="name">ETA Cancel Document</field>
            <field name="res_model">cancel.account.move</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
        <!--<record id="action_move_cancel_eta" model="ir.actions.act_window">
            <field name="name">ETA Cancel Document</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">cancel.account.move</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="cancel_account_move_view"/>
            <field name="context">{}</field>
            <field name="target">new</field>
            <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="binding_view_types">list</field>
        </record>-->
    </data>
</odoo>