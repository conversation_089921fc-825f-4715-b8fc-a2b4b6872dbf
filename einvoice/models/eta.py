# -*- coding: utf-8 -*-

import json
import requests
import urllib3
import ssl
import re
import base64
from datetime import datetime, timedelta

from odoo import fields, models, _, SUPERUSER_ID
from odoo.exceptions import UserError, ValidationError

# As https://sdk.sit.invoicing.eta.gov.eg/standard-error-response/
status_code_gn = {
    '200': 'Success',
    '400': 'NotReady or BadRequest or BadArgument',
    '401': 'Unauthorized',
    '403': 'Forbidden',
    '404': 'NotFound',
    '429': 'TooManyRequests',
    '500': 'InternalServerError',
    '501': 'NotImplemented',
    '503': 'ServiceUnavailable'
}

status_code_gn_pdf = {
    '200': 'Success',
    '400': 'NotReady',
    '404': 'NotFound',
}

status_code_gn_cancel = {
    '200': 'Success',
    '400': 'OperationPeriodOver or IncorrectState or ActiveReferencingDocuments',
    '403': 'Forbidden',
}


def round_to_decimal(num, rounding):

    if int(num) != num and len(str(num).split('.')[1]) > rounding:
        num = str(num)[:str(num).index('.')+rounding+2]
        if num[-1] >= '5':
            a = num[:-2-(not rounding)]       # integer part
            b = int(num[-2-(not rounding)])+1  # decimal part
            res = float(a)+b**(-rounding +
                               1) if a and b == 10 else float(a+str(b))
            return round(res, rounding)

        return float(num[:-1])
    else:
        return num

class ROCustomHttpAdapter(requests.adapters.HTTPAdapter):
    # "Transport adapter" that allows us to use custom ssl_context.

    def __init__(self, ssl_context=None, **kwargs):
        self.ssl_context = ssl_context
        super().__init__(**kwargs)

    def init_poolmanager(self, connections, maxsize, block=False):
        self.poolmanager = urllib3.poolmanager.PoolManager(
            num_pools=connections, maxsize=maxsize,
            block=block, ssl_context=self.ssl_context)


def ro_get_legacy_session():
    ctx = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
    ctx.options |= 0x4  # OP_LEGACY_SERVER_CONNECT
    session = requests.session()
    session.mount('https://', ROCustomHttpAdapter(ctx))
    return session

class EtaManage(models.Model):
    _name = 'eta.manage'
    _description = 'ETA Manage'

    def _create_eta_token(self, company):

        token_url = '%s/connect/token' % company.eta_id_srv_base_url

        eta_client_id = company.eta_client_id
        eta_client_secret = company.eta_client_secret

        payload = 'grant_type=client_credentials&client_id=%s&client_secret=%s&scope=InvoicingAPI' % (
            eta_client_id, eta_client_secret)

        headers = {
            "content-type": "application/x-www-form-urlencoded"
        }

        token_response = ro_get_legacy_session().post(
            token_url, data=payload, headers=headers)

        # if token_response.status_code == 200:
        token = token_response.json().get('access_token')
        expires_in = token_response.json().get('expires_in')

        if not token:
            error = token_response.json().get('error')
            error_description = token_response.json().get('error_description')
            error_uri = token_response.json().get('error_uri')

            raise UserError(_('Error %s description %s uri %s' %
                              (error, error_description, error_uri)))

        company.with_user(SUPERUSER_ID).write({
            'eta_generated_access_token': token,
            'eta_token_timeout': datetime.utcnow() + timedelta(seconds=expires_in)
        })

        return token

    def _get_json_data(self, invoices, current_company, store=False):

        total_invoices = []
        invoices = invoices[::-1]

        if not store:
            for move in invoices:
                if move.state != 'posted' or move.eta_state != 'signed':
                    continue

                eta_json_text = move.eta_json_text
                eta_json_text = eta_json_text.replace("'", '"')
                move_invoice = json.loads(eta_json_text)
                move_invoice["signatures"] = [{
                    "signatureType": "I",
                    "value": move.eta_sign
                    # mrmmrm
                    # "value": "MIIGywYJKoZIhvcNAQcCoIIGvDCCBrgCAQMxDTALBglghkgBZQMEAgEwCwYJKoZIhvcNAQcFoIID/zCCA/swggLjoAMCAQICEEFkOqRVlVar0F0n3FZOLiIwDQYJKoZIhvcNAQELBQAwSTELMAkGA1UEBhMCRUcxFDASBgNVBAoTC0VneXB0IFRydXN0MSQwIgYDVQQDExtFZ3lwdCBUcnVzdCBDb3Jwb3JhdGUgQ0EgRzIwHhcNMjAwMzMxMDAwMDAwWhcNMjEwMzMwMjM1OTU5WjBgMRUwEwYDVQQKFAxFZ3lwdCBUcnVzdCAxGDAWBgNVBGEUD1ZBVEVHLTExMzMxNzcxMzELMAkGA1UEBhMCRUcxIDAeBgNVBAMMF1Rlc3QgU2VhbGluZyBEZW1vIHVzZXIyMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApmVGVJtpImeq\u002BtIJiVWSkIEEOTIcnG1XNYQOYtf5\u002BDg9eF5H5x1wkgR2G7dvWVXrTsdNv2Q\u002Bgvml9SdfWxlYxaljg2AuBrsHFjYVEAQFI37EW2K7tbMT7bfxwT1M5tbjxnkTTK12cgwxPr2LBNhHpfXp8SNyWCxpk6eyJb87DveVwCLbAGGXO9mhDj62glVTrCFit7mHC6bZ6MOMAp013B8No9c8xnrKQiOb4Tm2GxBYHFwEcfYUGZNltGZNdVUtu6ty\u002BNTrSRRC/dILeGHgz6/2pgQPk5OFYRTRHRNVNo\u002BjG\u002BnurUYkSWxA4I9CmsVt2FdeBeuvRFs/U1I\u002BieKg1wIDAQABo4HHMIHEMAkGA1UdEwQCMAAwVAYDVR0fBE0wSzBJoEegRYZDaHR0cDovL21wa2ljcmwuZWd5cHR0cnVzdC5jb20vRWd5cHRUcnVzdENvcnBvcmF0ZUNBRzIvTGF0ZXN0Q1JMLmNybDAdBgNVHQ4EFgQUqzFDImtytsUbghbmtnl2/k4d5jEwEQYJYIZIAYb4QgEBBAQDAgeAMB8GA1UdIwQYMBaAFCInP8ziUIPmu86XJUWXspKN3LsFMA4GA1UdDwEB/wQEAwIGwDANBgkqhkiG9w0BAQsFAAOCAQEAxE3KpyYlPy/e3\u002B6jfz5RqlLhRLppWpRlKYUvH1uIhCNRuWaYYRchw1xe3jn7bLKbNrUmey\u002BMRwp1hZptkxFMYKTIEnNjOKCrLmVIuPFcfLXAQFq5vgLDSbnUhG/r5D\u002B50ndPucyUPhX3gw8gFlA1R\u002BtdNEoeKqYSo9v3p5qNANq12OuZbkhPg6sAD4dojWoNdlkc8J2ML0eq4a5AQvb4yZVb\u002BezqJyqKj83RekRZi0kMxoIm8l82CN8I/Bmp6VVNJRhQKhSeb7ShpdkZcMwcfKdDw6LW02/XcmzVl8NBBbLjKSJ/jxdL1RxPPza7RbGqSx9pfyav5\u002BAxO9sXnXXc5jGCApIwggKOAgEBMF0wSTELMAkGA1UEBhMCRUcxFDASBgNVBAoTC0VneXB0IFRydXN0MSQwIgYDVQQDExtFZ3lwdCBUcnVzdCBDb3Jwb3JhdGUgQ0EgRzICEEFkOqRVlVar0F0n3FZOLiIwCwYJYIZIAWUDBAIBoIIBCjAYBgkqhkiG9w0BCQMxCwYJKoZIhvcNAQcFMBwGCSqGSIb3DQEJBTEPFw0yMDEwMzAxMzAzNTRaMC8GCSqGSIb3DQEJBDEiBCBugiBr7UM5Ei/7dQYuvMbC4joA8aJ\u002B1zstdWsSKBCZnTCBngYLKoZIhvcNAQkQAi8xgY4wgYswgYgwgYUEIAJA8uO/ek3l9i3ZOgRtPhGWwwFYljbeJ7yAgEnyYNCWMGEwTaBLMEkxCzAJBgNVBAYTAkVHMRQwEgYDVQQKEwtFZ3lwdCBUcnVzdDEkMCIGA1UEAxMbRWd5cHQgVHJ1c3QgQ29ycG9yYXRlIENBIEcyAhBBZDqkVZVWq9BdJ9xWTi4iMAsGCSqGSIb3DQEBAQSCAQCDrv7l41A\u002BN625X9UnEYIXENciomWehs4UgFcgQgsTP7eaNoOPk4fQDUyuW4bIArNWnTn5qrTrSCh/hFA4HGNV8942KNQaUDwSIrKImE2aMpsjyrfV6rIP2R9HJnk0Gx7M7lT0dtwV1Yw0GyZ\u002BNQjQZrGrH\u002BI5RXixZPPOm2jxI9SwyX0X9yFIT2BvVkUhb9qqzxP\u002BX/HL3yI3LlIHcITgckKzucjjCiG61EsasCR/gHhGjUbacJXJvC3IQbk\u002BLNUi7tqGM2rCWZZaFcaKTUCU6vLfXgwcbIdWidOIf6xVFE4Gy6Zx/k3S/7/4KprPyKeCm5hZVll19SIcOKNGRSzW"
                }]

                total_invoices.append(move_invoice)
            return total_invoices

        rounding = self.env['decimal.precision'].precision_get(
            'Product Price')
        rounding_uom = self.env['decimal.precision'].precision_get(
            'Product Unit of Measure')

        rounding = rounding if rounding > rounding_uom else rounding_uom
        rounding = rounding if rounding <= 5 else 5

        for move in invoices:

            if move.state != 'posted' or move.eta_state in ['tosign', 'signed', 'accepted', 'submitted', 'valid']:
                continue

            invoice_lines_dict = []
            invoice_line_ids_all = move.invoice_line_ids.filtered(
                lambda line: line.display_type == 'product')
            invoice_line_ids = invoice_line_ids_all.filtered(
                lambda line: line.price_unit >= 0)

            totalMoveDiscount = totalMoveSales = totalMoveItemDiscount = 0
            totalMoveAmount = netMoveAmount = extraDiscountAmount = 0

            # Get global discount
            discount_lines = invoice_line_ids_all.filtered(
                lambda line: line.price_unit < 0)

            if discount_lines:
                extraDiscountAmount = abs(
                    sum(discount_lines.mapped('price_unit')))

            moveTaxs = []
            errors = []

            for line in invoice_line_ids:
                taxs = []

                TotalTaxableFees = totalLineTaxs = 0
                line_tax_ids = line.tax_ids

                # TODO:for forign currency need to work same

                amountEGP = round_to_decimal(line.price_unit, rounding)
                line_quantity = round_to_decimal(line.quantity, rounding)
                amountSold = 0

                if move.currency_id.name != 'EGP':
                    amountSold = amountEGP

                    move_currency_rate = self.env['res.currency']._get_conversion_rate(
                        move.company_id.currency_id, move.currency_id, move.company_id, move.date or fields.Date.context_today(self))

                    if not move_currency_rate:
                        move_currency_rate = 1
                        errors.append(
                            'Line product [%s] Currency Rate' % line.product_id.name)
                    else:
                        move_currency_rate = round_to_decimal(
                            1/move_currency_rate, rounding)
                        amountEGP = round_to_decimal(
                            amountEGP * move_currency_rate, rounding)

                for tax in line_tax_ids.filtered(lambda tax: tax.price_include):
                    amountEGP = round_to_decimal(
                        amountEGP / (1+(abs(tax.amount)/100)), rounding)

                discountAmount = (line_quantity * amountEGP) * \
                    (line.discount / 100)
                netTotal = (line_quantity * amountEGP) - discountAmount
                valueDifference = totalLineDiscount = totalLineSales = 0.0

                tax_include_base_amount_ids = line_tax_ids.filtered(
                    lambda tax: tax.include_base_amount)
                tax_not_include_base_amount_ids = line_tax_ids.filtered(
                    lambda tax: not tax.include_base_amount)
                total_after_tax_include = netTotal

                for tax in tax_include_base_amount_ids:
                    # TODO: add other taxes
                    eta_tax_amount = 0

                    # T1/T2/T4 tax amount
                    if tax.eta_code in ['T1', 'T2', 'T4']:
                        eta_tax_amount = round_to_decimal(
                            (abs(tax.amount)/100)*total_after_tax_include, rounding)

                    if tax.amount == 8:
                        total_after_tax_include += eta_tax_amount

                    if tax.eta_code in ['T1', 'T2']:
                        totalLineTaxs += eta_tax_amount
                    elif tax.eta_code == 'T4':
                        totalLineTaxs -= eta_tax_amount

                    taxs.append({
                        "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "amount": round_to_decimal(eta_tax_amount, rounding),
                        "subType": tax.eta_sub_code if tax.eta_sub_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "rate": round_to_decimal(abs(tax.amount), rounding)
                    })

                    duplicateTax = False

                    for item in moveTaxs:
                        if item['taxType'] == tax.eta_code:
                            duplicateTax = item

                    if duplicateTax:

                        duplicateTax['amount'] += eta_tax_amount

                    else:
                        moveTaxs.append({
                            "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                            "amount": eta_tax_amount,
                        })

                for tax in tax_not_include_base_amount_ids:
                    # TODO: add other taxes
                    eta_tax_amount = 0

                    # T1/T2/T4 tax amount
                    if tax.eta_code in ['T1', 'T2', 'T4']:
                        eta_tax_amount = round_to_decimal(
                            (abs(tax.amount)/100)*total_after_tax_include, rounding)

                    if tax.eta_code in ['T1', 'T2']:
                        totalLineTaxs += eta_tax_amount
                    elif tax.eta_code == 'T4':
                        totalLineTaxs -= eta_tax_amount

                    taxs.append({
                        "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "amount": round_to_decimal(eta_tax_amount, rounding),
                        "subType": tax.eta_sub_code if tax.eta_sub_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "rate": round_to_decimal(abs(tax.amount), rounding)
                    })

                    duplicateTax = False

                    for item in moveTaxs:
                        if item['taxType'] == tax.eta_code:
                            duplicateTax = item

                    if duplicateTax:

                        duplicateTax['amount'] += eta_tax_amount

                    else:
                        moveTaxs.append({
                            "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                            "amount": eta_tax_amount,
                        })

                # TODO: item discount after tax
                itemsLineDiscount = 0
                # totalMoveItemDiscount += round_to_decimal(
                #    itemsLineDiscount, rounding)

                product_uom_eta_code = line.product_uom_id.eta_code if line.product_uom_id.eta_code else line.product_id.uom_id.eta_code
                if not product_uom_eta_code:
                    errors.append(
                        'Line product [%s] UOM Code' % line.product_id.name)

                if line.product_id.eta_code_type:
                    eta_code_type = line.product_id.eta_code_type.upper()
                else:
                    eta_code_type = False

                eta_item_code = line.product_id.eta_item_code

                if len(line.name) > 500:
                    errors.append(
                        'Line product [%s] Description must be less than 500 char.' % line.product_id.name)

                invoice_line_without_tax = {
                    "description": line.name.replace('"', '|').replace("'", "|").replace('\xa0', ' ') if line.name else errors.append('Line product [%s] Description' % line.product_id.name),
                    "itemType": eta_code_type,
                    "unitType": product_uom_eta_code,
                    "quantity": line_quantity,
                    "salesTotal": round_to_decimal(line_quantity * amountEGP, rounding),
                    "valueDifference": round_to_decimal(valueDifference, rounding),
                    "netTotal": round_to_decimal(netTotal, rounding),
                    "itemsDiscount": round_to_decimal(itemsLineDiscount, rounding),
                    "unitValue": {
                        "currencySold": move.currency_id.name,
                        "amountEGP": round_to_decimal(amountEGP, rounding),
                    },
                    "discount": {
                        "rate": round_to_decimal(line.discount, rounding),
                        "amount": round_to_decimal(discountAmount, rounding)
                    }
                }

                if eta_code_type == 'EGS':
                    invoice_line_without_tax["itemCode"] = "EG-{}-{}".format(
                        current_company.eta_id, eta_item_code) if eta_item_code else errors.append('Line product [%s] Product Code' % line.product_id.name)
                elif eta_code_type == 'GS1':
                    invoice_line_without_tax["itemCode"] = eta_item_code if eta_item_code else errors.append(
                        'Product Code')
                else:
                    errors.append(
                        'Line product [%s] Code Type or Product Code' % line.product_id.name)

                if line.product_id.default_code:
                    invoice_line_without_tax["internalCode"] = str(
                        line.product_id.default_code)

                elif self.env.company.eta_is_preproduction:
                    raise UserError(
                        _('product internal reference is required on preproduction.'))

                if move.currency_id.name != 'EGP':

                    invoice_line_without_tax['unitValue']['currencyExchangeRate'] = move_currency_rate

                    invoice_line_without_tax['unitValue']['amountSold'] = round_to_decimal(
                        amountSold, rounding)

                totalMoveDiscount += round_to_decimal(discountAmount, rounding)
                totalMoveSales += round_to_decimal(
                    line_quantity * amountEGP, rounding)

                totalLineDiscount += discountAmount
                totalLineSales += line_quantity * amountEGP

                netMoveAmount += round_to_decimal(
                    totalLineSales - totalLineDiscount, rounding)
                totalMoveAmount += round_to_decimal(
                    totalLineSales - totalLineDiscount + totalLineTaxs, rounding)

                invoice_line_without_tax['total'] = round_to_decimal(
                    netTotal + TotalTaxableFees + valueDifference + totalLineTaxs, rounding)
                invoice_line_without_tax['totalTaxableFees'] = round_to_decimal(
                    TotalTaxableFees, rounding)

                invoice_line_without_tax['taxableItems'] = taxs

                invoice_lines_dict.append(invoice_line_without_tax)

            for moveTax in moveTaxs:
                moveTax['amount'] = round_to_decimal(
                    moveTax['amount'], rounding)

            eta_date_time_issued = ''

            if move.eta_date_time_issued:
                eta_date_time_issued = move.eta_date_time_issued.strftime("%Y-%m-%dT%H:%M:%SZ")
            else:
                errors.append('Date Time Issued')

            move_invoice = {
                "issuer": {
                    "address": {
                        "branchID": move.eta_company_branch_id.eta_branchId if move.eta_company_branch_id.eta_branchId else errors.append('Company branchId'),
                        "country": move.eta_company_branch_id.eta_country_id.code if move.eta_company_branch_id.eta_country_id else errors.append('Company Country'),
                        "governate": move.eta_company_branch_id.eta_governate if
                        move.eta_company_branch_id.eta_governate else errors.append('Company Governate'),
                        "regionCity": move.eta_company_branch_id.eta_region_city if
                        move.eta_company_branch_id.eta_region_city else errors.append('Company City'),
                        "street": move.eta_company_branch_id.eta_street.replace(
                            '"', '|').replace("'", "|").replace('\xa0', ' ') if
                        move.eta_company_branch_id.eta_street else errors.append('Company Street'),
                        "buildingNumber": move.eta_company_branch_id.eta_building_number if move.eta_company_branch_id.eta_building_number else errors.append('Company Building Number'),
                        # "postalCode": "68030",
                        # "floor": "1",
                        # "room": "123",
                        # "landmark": "7660 Melody Trail",
                        # "additionalInformation": "beside Townhall"
                    },
                    "type": current_company.eta_type if current_company.eta_type else errors.append('Company Type'),
                    "id": current_company.eta_id if current_company.eta_id else errors.append('Company Id'),
                    "name": current_company.eta_name.replace('"', '|').replace("'", "|").replace('\xa0', ' ') if \
                    current_company.eta_name else errors.append('Name')
                },
                "receiver": {
                    "type": move.partner_id.eta_type if move.partner_id.eta_type else errors.append('Partner Type'),
                    "name": move.partner_id.eta_name.replace('"', '|').replace("'", "|").replace('\xa0', ' ') \
                    if move.partner_id.eta_name else errors.append('Partner Name')
                },
                "documentType": move.eta_document_type if move.eta_document_type else errors.append('Document Type'),
                # "documentTypeVersion": "0.9", mrmmrm
                "documentTypeVersion": move.eta_document_type_version if move.eta_document_type_version else errors.append('Document Type Version'),
                "dateTimeIssued": eta_date_time_issued,
                "taxpayerActivityCode": str(move.eta_taxpayer_activity_code_id.code) if move.eta_taxpayer_activity_code_id else errors.append('Taxpayer Activity Code'),
                "internalID": move.name,
                # "purchaseOrderReference": "P-233-A6375",
                # "purchaseOrderDescription": "purchase Order description",
                # "salesOrderReference": "1231",
                # "salesOrderDescription": "Sales Order description",
                # "proformaInvoiceNumber": "SomeValue",
                # "payment": {
                #    "bankName": "SomeValue",
                #    "bankAddress": "SomeValue",
                #    "bankAccountNo": "SomeValue",
                #    "bankAccountIBAN": "",
                #    "swiftCode": "",
                #    "terms": "SomeValue"
                # },
                # "delivery": {
                #    "approach": "SomeValue",
                #    "packaging": "SomeValue",
                #    "dateValidity": "2020-09-28T09:30:10Z",
                #    "exportPort": "SomeValue",
                #    "grossWeight": 10.50,
                #    "netWeight": 20.50,
                #    "terms": "SomeValue"
                # },
                "invoiceLines":
                invoice_lines_dict,
                "totalDiscountAmount": round_to_decimal(totalMoveDiscount, rounding),
                "totalSalesAmount": round_to_decimal(totalMoveSales, rounding),
                "netAmount": round_to_decimal(netMoveAmount, rounding),
                "taxTotals":
                moveTaxs,
                "totalAmount": round_to_decimal(totalMoveAmount - extraDiscountAmount, rounding),
                "extraDiscountAmount": extraDiscountAmount,
                "totalItemsDiscountAmount": round_to_decimal(totalMoveItemDiscount, rounding)
            }
            
            if move.ref:
                move_invoice['purchaseOrderReference'] = move.ref

            if move.partner_id.eta_street:
                if move_invoice['receiver'].get('address') is None:
                    move_invoice['receiver']['address'] = {}
                move_invoice['receiver']['address']['street'] = move.partner_id.eta_street.replace(
                    '"', '|').replace("'", "|").replace('\xa0', ' ')
            elif move.partner_id.eta_type == "B":
                errors.append('Partner Street')

            if move.partner_id.eta_id:
                move_invoice['receiver']['id'] = move.partner_id.eta_id
            elif move.partner_id.eta_type == "B" or totalMoveAmount >= current_company.eta_type_person_minimum:
                errors.append('Partner Id')

            if move.partner_id.eta_country_id:
                if move_invoice['receiver'].get('address') is None:
                    move_invoice['receiver']['address'] = {}
                move_invoice['receiver']['address']['country'] = move.partner_id.eta_country_id.code
            elif move.partner_id.eta_type == "B":
                errors.append('Partner Country')

            if move.partner_id.eta_governate:
                if move_invoice['receiver'].get('address') is None:
                    move_invoice['receiver']['address'] = {}
                move_invoice['receiver']['address']['governate'] = move.partner_id.eta_governate
            elif move.partner_id.eta_type == "B":
                errors.append('Partner Governate')

            if move.partner_id.eta_region_city:
                if move_invoice['receiver'].get('address') is None:
                    move_invoice['receiver']['address'] = {}
                move_invoice['receiver']['address']['regionCity'] = move.partner_id.eta_region_city
            elif move.partner_id.eta_type == "B":
                errors.append('Partner City')

            if move.partner_id.eta_building_number:
                if move_invoice['receiver'].get('address') is None:
                    move_invoice['receiver']['address'] = {}
                move_invoice['receiver']['address']['buildingNumber'] = move.partner_id.eta_building_number
            elif move.partner_id.eta_type == "B":
                errors.append('Partner Building Number')

            # Make issues with sign
            # TODO: make it like get raw invoice
            '''if move.eta_document_type in ['c', 'd'] and move.eta_invoice_uuid:
                move_invoice['references'] = [move.eta_invoice_uuid]'''

            if len(errors) > 0:
                raise ValidationError(
                    _('Fields are required {}'.format(errors)))

            # date can't be in future and utc time
            now = datetime.utcnow()
            then = datetime.utcfromtimestamp(
                int(datetime.timestamp(move.eta_date_time_issued)))
            diff = (now - then) / timedelta(seconds=1)

            if diff < 0:
                raise ValidationError(
                    _("Time should be in UTC and not future."))

            move.write(
                # {'eta_json_text': move_invoice, 'eta_state': 'signed'}) mrmmrm
                {'eta_json_text': move_invoice, 'eta_state': 'tosign'})

            total_invoices.append(move_invoice)

        return total_invoices

    def _sign_eta(self, invoices):

        current_company = invoices.mapped('company_id')

        if len(current_company) > 1:
            raise UserError(_('Please choose only one company.'))

        self._get_json_data(invoices, current_company, store=True)

        return True

    def _send_eta(self, invoices):

        def _after_token(url, invoices, current_company, token):

            total_invoices = self._get_json_data(
                invoices, current_company, store=False)
            if not total_invoices:
                return False

            payload = {
                "documents":
                total_invoices
            }
            payload = json.dumps(payload, ensure_ascii=False)

            headers = {
                'Accept-Language': 'en',
                'Authorization': 'Bearer %s' % (token),
                'Content-Type': 'application/json'
            }

            response = ro_get_legacy_session().post(
                url, headers=headers, data=payload.encode('utf-8'))

            return response

        current_company = invoices.mapped('company_id')

        if len(current_company) > 1:
            raise UserError(_('Please choose only one company.'))

        url = '%s/api/v1/documentsubmissions' % (
            current_company.eta_api_base_url)
        if not current_company.eta_token_timeout or datetime.utcnow() > current_company.eta_token_timeout:
            token = self._create_eta_token(current_company)
        else:
            token = current_company.eta_generated_access_token

        response = _after_token(url, invoices, current_company, token)

        if response == False:
            return True

        elif response.status_code in [202, 200]:
            rejected = response.json().get('rejectedDocuments')
            accepted = response.json().get('acceptedDocuments')
            if rejected:

                for move_item in rejected:
                    move = invoices.filtered(
                        lambda inv: inv.name == move_item['internalId'])
                    errors = move_item.get('error')['details']

                    err = ''
                    for error in errors:
                        err += '%s\n' % (error['message'])

                    move.write({'eta_state': 'rejected',
                                'eta_reject_reason': err,
                                'eta_json_text': False})

            elif accepted:
                for move_item in accepted:
                    move = invoices.filtered(
                        lambda inv: inv.name == move_item['internalId'])
                    uuid = move_item['uuid']

                    move.write({'eta_uuid': uuid, 'eta_state': 'accepted', 'eta_reject_reason': False, 'eta_doc_pdf': False,
                                'eta_doc_pdf_ar': False, 'eta_doc_pdf_name': False, 'eta_sign': False,
                                'eta_invalid_reason': False, 'eta_cancel_reason': False, 'eta_json_text': False})

            # On 202 response if validation not complete
            else:
                raise UserError(response)

        elif response.status_code == 401:
            raise ValidationError(_('Not Authorized'))
        else:
            error_description = status_code_gn.get(
                str(response.status_code)) or response.text
            raise UserError(_('Error %s %s' %
                              (response.status_code, error_description)))

        return True

    def get_eta_document_state(self, invoices):
        def _after_token(invoices, current_company, token):
            invoices = invoices[::-1]
            response = False

            for move in invoices:

                if not move or move.eta_state not in ['accepted', 'submitted', 'valid']:
                    continue

                url = '%s/api/v1.0/documents/%s/details' % (
                    current_company.eta_api_base_url, move.eta_uuid)

                headers = {
                    'Accept-Language': 'en',
                    'Authorization': 'Bearer %s' % (token),
                    'Content-Type': 'application/json'
                }

                payload = {}

                response = ro_get_legacy_session().get(url, headers=headers, data=payload)
                if response.status_code in [202, 200]:
                    status = response.json().get('status').lower()

                    if status == 'valid':

                        move.write(
                            {'eta_state': 'valid'})

                    elif status == 'invalid':
                        errors = []
                        if response.json().get('validationResults'):
                            errors = response.json().get('validationResults').get('validationSteps')

                        err = ''
                        for error in errors:
                            if error['status'] and error['status'].lower() == 'invalid':
                                msgs = error['error']['innerError']
                                for msg in msgs:
                                    msg = msg['error']

                                    product_eta_code = msg[msg.find(
                                        "[")+1:msg.find("]")]

                                    product = self.env['product.product'].search(
                                        [('eta_item_code', '=ilike', product_eta_code)])

                                    if product:
                                        err += '%s: Product %s %s \n' % (
                                            error['stepName'], product.name, msg)
                                    else:
                                        err += '%s: %s \n' % (
                                            error['stepName'], msg)

                        move.write(
                            {'eta_state': 'invalid', 'eta_invalid_reason': err})

                    elif status == 'rejected':
                        move.write({
                            'eta_state': 'rejected',
                            'eta_doc_pdf': False,
                            'eta_doc_pdf_ar': False,
                            'eta_doc_pdf_name': False
                        })

                    elif status == 'cancelled':
                        move.write({
                            'eta_state': 'canceled',
                            'eta_doc_pdf': False,
                            'eta_doc_pdf_ar': False,
                            'eta_doc_pdf_name': False
                        })

                    elif status == 'submitted':
                        move.write({
                            'eta_state': 'submitted'
                        })

                    else:
                        raise UserError(_('Check Later'))

            return response

        current_company = invoices.mapped('company_id')

        if len(current_company) > 1:
            raise UserError(_('Please choose only one company.'))

        if not current_company.eta_token_timeout or datetime.utcnow() > current_company.eta_token_timeout:
            token = self._create_eta_token(current_company)
        else:
            token = current_company.eta_generated_access_token

        response = _after_token(invoices, current_company, token)

        if response == False:
            return True

        if response.status_code == 401:
            raise ValidationError(_('Not Authorized'))

        elif response.status_code not in [202, 200]:
            error_description = status_code_gn.get(
                str(response.status_code)) or response

            error = 'Error %s %s' % (response.status_code, error_description)

            if response.status_code == 404:
                error += ' or check later'

            raise UserError(_(error))

        return True

    def eta_document_cancel(self, invoices):
        def _after_token(invoices, current_company, token):

            invoices = invoices[::-1]
            response = False

            for move in invoices:

                if not move or move.eta_state != 'valid':
                    continue

                url = '%s/api/v1.0/documents/state/%s/state' % (
                    self.env.company.eta_api_base_url, move.eta_uuid)

                headers = {
                    'Accept-Language': 'en',
                    'Authorization': 'Bearer %s' % (token),
                    'Content-Type': 'application/json'
                }

                errors = []

                payload = {
                    "status": "cancelled",
                    "reason": move.eta_cancel_reason if move.eta_cancel_reason else errors.append('Invoice cancel reason')
                }

                if len(errors) > 0:
                    raise UserError(_('Fields are required {}'.format(errors)))

                payload = json.dumps(payload, ensure_ascii=False)

                response = ro_get_legacy_session().put(
                    url, headers=headers, data=payload.encode('utf-8'))

                if response.status_code in [202, 200] and response.text == 'true':

                    # to cancel invoice in odoo base functionality
                    move.button_cancel()

                    move.write({
                        'eta_state': 'tocancel',
                        'eta_doc_pdf': False,
                        'eta_doc_pdf_ar': False,
                        'eta_doc_pdf_name': False
                    })

            if response and not response.status_code:
                raise UserError(_('Something Wrong'))

            return response

        current_company = invoices.mapped('company_id')

        if len(current_company) > 1:
            raise UserError(_('Please choose only one company.'))

        if not current_company.eta_token_timeout or datetime.utcnow() > current_company.eta_token_timeout:
            token = self._create_eta_token(current_company)
        else:
            token = current_company.eta_generated_access_token

        response = _after_token(invoices, current_company, token)

        if response == False:
            return True

        if response.status_code == 401:
            raise ValidationError(_('Not Authorized'))

        elif response.status_code not in [202, 200]:
            error_description = status_code_gn_cancel.get(
                str(response.status_code)) or response
            raise UserError(_('Error %s %s' %
                              (response.status_code, error_description)))

        return True

    def get_eta_document_pdf(self, invoices):
        def _after_token(invoices, current_company, token):

            response = False
            invoices = invoices[::-1]

            languages = ['en', 'ar']
            for move in invoices:

                if not move or move.eta_state != 'valid':
                    continue

                url = '%s/api/v1.0/documents/%s/pdf' % (
                    current_company.eta_api_base_url, move.eta_uuid)

                for lang in languages:
                    headers = {
                        'Accept-Language': lang,
                        'Authorization': 'Bearer %s' % (token),
                        'Content-Type': 'application/json'
                    }

                    payload = {}

                    response = ro_get_legacy_session().get(url, headers=headers, data=payload)

                    if response.status_code in [202, 200]:

                        if lang == 'en':
                            move.write({
                                'eta_doc_pdf': base64.b64encode(response.content),
                                'eta_doc_pdf_name': move.name,
                            })
                        elif lang == 'ar':
                            move.write({
                                'eta_doc_pdf_ar': base64.b64encode(response.content),
                                'eta_doc_pdf_name': move.name,
                            })

            return response

        current_company = invoices.mapped('company_id')

        if len(current_company) > 1:
            raise UserError(_('Please choose only one company.'))

        if not current_company.eta_token_timeout or datetime.utcnow() > current_company.eta_token_timeout:
            token = self._create_eta_token(current_company)
        else:
            token = current_company.eta_generated_access_token

        response = _after_token(invoices, current_company, token)

        if response == False:
            return True

        if response.status_code == 401:
            raise ValidationError(_('Not Authorized'))

        elif response.status_code not in [202, 200]:
            error_description = status_code_gn_pdf.get(
                str(response.status_code)) or response
            raise UserError(_('Error %s %s' %
                              (response.status_code, error_description)))

        return True

    def get_eta_notifications(self, language):

        company_ids = self.env['res.company'].search(
            [('eta_client_id', '!=', False)])

        for company in company_ids:
            def _after_token(token, channel, odoo_bot_partner):

                response = False
                date_time_from = company.date_from_notifications
                date_time_to = fields.Datetime.now()

                # Criteria limit in hours should be less than or equal 120 hour
                total_hours = date_time_to - date_time_from
                total_hours = total_hours.total_seconds() / 3600

                if total_hours > 120:
                    date_time_from = date_time_to - timedelta(hours=120)

                url = '%s/api/v1.0/notifications/taxpayer' % (
                    company.eta_api_base_url)

                headers = {
                    'Accept-Language': language,
                    'Authorization': 'Bearer %s' % (token),
                    'Content-Type': 'application/json'
                }

                payload = {
                    'dateFrom': date_time_from.strftime("%Y-%m-%dT%H:%MZ"),
                    'dateTo': date_time_to.strftime("%Y-%m-%dT%H:%MZ")
                }

                response = ro_get_legacy_session().get(url, headers=headers, params=payload)

                if response.status_code in [202, 200]:
                    notifications = response.json().get('result')

                    if not notifications:
                        return False

                    for notification in notifications:
                        message = 'Company: {}: {}<br>\n{}<br>\n{}'.format(company.name,
                                                                           notification.get(
                                                                               'notificationSubject') or '', notification.get('typeName'),
                                                                           notification.get('metadata'))

                        channel.message_post(
                            body=message, subtype_xmlid='mail.mt_comment', author_id=odoo_bot_partner.id,
                            eta_message=True)

                    company.with_user(SUPERUSER_ID).write(
                        {'date_from_notifications': date_time_to})

                return response

            channel = self.env.ref('einvoice_base.channel_eta')
            odoo_bot_partner = self.env.ref('base.partner_root')

            if not company.eta_token_timeout or datetime.utcnow() > company.eta_token_timeout:
                token = self._create_eta_token(company)
            else:
                token = company.eta_generated_access_token

            response = _after_token(
                token, channel, odoo_bot_partner)

            if response == False:
                return True

            if response.status_code == 401:
                raise ValidationError(_('Not Authorized'))

            elif response.status_code not in [202, 200]:
                error_description = status_code_gn.get(
                    str(response.status_code)) or response

                error = 'Error %s %s' % (
                    response.status_code, error_description)

                if response.status_code == 404:
                    error += ' error 404'

                channel.message_post(
                    body=error, subtype_xmlid='mail.mt_comment', author_id=odoo_bot_partner.id)

        return True
