# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class ResCompany(models.Model):
    _inherit = 'res.company'

    eta_id = fields.Char(store=True, related='partner_id.eta_id',
                         readonly=False)

    eta_type = fields.Selection(
        store=True, related='partner_id.eta_type', readonly=False
    )

    eta_name = fields.Char(store=True, related='partner_id.eta_name',
                           readonly=False)
    
    #For notifications
    
    date_from_notifications = fields.Datetime('date_from_notifications', default=fields.Datetime.now)
