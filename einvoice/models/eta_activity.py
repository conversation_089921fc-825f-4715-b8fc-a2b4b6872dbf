# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class EtaActivityTypes(models.Model):

    _name = "eta.activity.types"
    _check_company_auto = True
    _description = "ETA Activity Types"

    name = fields.Char(
        string='Name',
        translate=True
    )

    code = fields.Char(
        string='Code',
        index=True,
        required=True
    )

    eta_company_branch_id = fields.Many2one(
        string='Company Branch',
        comodel_name='eta.company.branch',
        ondelete='restrict',
        required=True,
        help='Mandatory when issuer is of type B, \
            otherwise optional. The code of the branch as registered with tax authority for the \
            company submitting the document.[1234]'
    )

    active = fields.Boolean(string='Active', default=True)
    
    company_id = fields.Many2one(related='eta_company_branch_id.company_id', store=True, readonly=True)

