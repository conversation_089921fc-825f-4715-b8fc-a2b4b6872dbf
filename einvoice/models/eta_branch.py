# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class EtaCompanyBranch(models.Model):

    _name = "eta.company.branch"
    _check_company_auto = True
    _description = "ETA Company Branch"

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    company_id = fields.Many2one(
        'res.company', 'Company', required=True, index=True, default=lambda self: self.env.company)

    '''def init(self):
        self.env.cr.execute("""
            INSERT INTO eta_company_branch  ("name", "eta_branchId", "eta_country_id", 
            "eta_governate", 
            "eta_region_city", "eta_street", "eta_building_number", "eta_postalCode", 
            "eta_floor", 
            "eta_room", "eta_landmark", "eta_additionalInformation")
            select concat('[',"eta_branchId",']', ' ',"eta_street",' ',
            "eta_governate",' ',"eta_region_city"), "eta_branchId", "eta_country_id", 
            "eta_governate", "eta_region_city", "eta_street", 
            "eta_building_number", "eta_postalCode", "eta_floor", "eta_room", "eta_landmark", 
            "eta_additionalInformation" from res_partner where "eta_branchId" is not null;
            update account_move  set eta_company_branch_id = 33 where eta_state != 'draft';
        """)'''

    # Address
    # https://sdk.sit.invoicing.eta.gov.eg/codes/branch/
    eta_branchId = fields.Char(string='Branch Id', help='Mandatory when issuer is of type B, \
        otherwise optional. The code of the branch as registered with tax authority for the \
            company submitting the document.[1234]', index=True, required=True)
    eta_country_id = fields.Many2one('res.country', string='Issuer Country', help='Country \
        represented by ISO-3166-2 2 symbol code of the countries. \
            Must be EG for internal business issuers.[EG]',
                                     required=True)
    eta_governate = fields.Char(string='Governate',
                                help='Governorate information as textual value.[Giza Governorate]',
                                required=True)
    eta_region_city = fields.Char(
        string='Region City', help='Region and city information as textual value.[Dokki]',
        required=True)

    eta_street = fields.Char(
        string='Street', help='street information.[17 Nabil Al Wakad]',
        required=True)
    eta_building_number = fields.Char(
        string='Building Number', help='building information.[17]')

    # optional
    eta_postalCode = fields.Char(
        string='Postal Code', help="Optional: Postal code")
    eta_floor = fields.Char(string='Floor', help="Optional: the floor number")
    eta_room = fields.Char(
        string='Room', help="Optional: the room/flat number in the floor")
    eta_landmark = fields.Char(
        string='Landmark', help="Optional: nearest landmark to the address")

    eta_additionalInformation = fields.Text(
        string='Additional Information', help="Optional: any additional information to the address")

    

    @api.depends('eta_branchId', 'eta_country_id', 'eta_governate', 'eta_region_city', 'eta_street')
    def _compute_name(self):
        for record in self:

            if record.eta_branchId and record.eta_country_id and record.eta_governate and\
                    record.eta_region_city and record.eta_street:
                record.name = "[{}] {} {}, {}, {}".format(
                    record.eta_branchId, record.eta_street, record.eta_governate,
                    record.eta_region_city, record.eta_country_id.name)
            else:
                record.name = ''
