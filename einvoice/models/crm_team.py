# -*- coding: utf-8 -*-

from odoo import fields, models, _

class CrmTeam(models.Model):
    _inherit = 'crm.team'

    eta_company_branch_id = fields.Many2one(
        string='Company Branch',
        comodel_name='eta.company.branch',
        ondelete='restrict',
        help='Mandatory when issuer is of type B, \
            otherwise optional. The code of the branch as registered with tax authority for the \
            company submitting the document.[1234]'
    )