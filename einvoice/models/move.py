# -*- coding: utf-8 -*-

from datetime import datetime
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


@api.model
def _lang_get(self):
    return self.env['res.lang'].get_installed()


class AccountMove(models.Model):
    _inherit = 'account.move'

    eta_uuid = fields.Char(
        string='UUID',
        readonly=True,
        copy=False,
        tracking=True
    )

    eta_invoice_uuid = fields.Char(
        string='Invoice UUID',
        copy=False,
        tracking=True
    )

    eta_reject_reason = fields.Text(
        string='Reject Reason',
        readonly=True,
        copy=False,
        tracking=True
    )
    eta_invalid_reason = fields.Text(
        string='Invalid Reason',
        readonly=True,
        copy=False,
        tracking=True
    )
    eta_cancel_reason = fields.Text(
        string='Cancel Reason',
        copy=False,
        tracking=True
    )

    eta_json_text = fields.Text(
        string='Json Text',
        readonly=True,
        copy=False
    )
    eta_sign = fields.Text(
        string='Sign Text',
        readonly=True,
        copy=False
    )

    eta_doc_pdf = fields.Binary(
        string='PDF English',
        readonly=True,
        copy=False
    )
    eta_doc_pdf_ar = fields.Binary(
        string='PDF Arabic',
        readonly=True,
        copy=False
    )
    eta_doc_pdf_name = fields.Char(
        string='Name',
        readonly=True,
        copy=False
    )

    # states is tocancel till time then convert to canceled
    # TODO: cron job to get status
    eta_state = fields.Selection(selection=[
        ('draft', 'Draft'),
        ('tosign', 'ToSign'),
        ('signed', 'Signed'),
        ('accepted', 'Accepted'),
        ('submitted', 'Submitted'),
        ('valid', 'Valid'),
        ('invalid', 'Invalid'),
        ('rejected', 'Rejected'),
        ('tocancel', 'tocancel'),
        ('canceled', 'Canceled')
    ], string='ETA Status', readonly=True, copy=False, tracking=True, default='draft')

    eta_document_type_version = fields.Selection(selection=[
        ('0.9', '0.9'),
        ('1.0', '1.0'),
    ], string='Type Version', default='1.0')

    eta_is_preproduction = fields.Boolean(
        related='company_id.eta_is_preproduction')

    #eta_lang = fields.Selection(_lang_get, string='Document Language')
    eta_document_type = fields.Selection(
        string='Document Type',
        selection=[('i', 'Invoice'), ('c', 'Credit Note'),
                   ('d', 'Debit Note')],
        compute='_compute_eta_document_type',
        readonly=False, store=True,
        help='Document type name.[i]')

    @api.depends('move_type')
    def _compute_eta_document_type(self):
        for record in self:
            if record.move_type == 'out_invoice':
                record.eta_document_type = 'i'
            elif record.move_type == 'out_refund':
                record.eta_document_type = 'c'
            else:
                record.eta_document_type = 'i'

    eta_date_time_issued = fields.Datetime(string='Date Time Issued',
                                           help='The date and time when the document was issued. \
                                             Date and time cannot be in future. \
                                            Time to be supplied in UTC timezone.[2015-02-13T13:15:00Z]')

    eta_company_branch_id = fields.Many2one(
        string='Company Branch',
        comodel_name='eta.company.branch',
        ondelete='restrict',
        help='Mandatory when issuer is of type B, \
            otherwise optional. The code of the branch as registered with tax authority for the \
            company submitting the document.[1234]'
    )

    # As https://sdk.sit.invoicing.eta.gov.eg/codes/activity-types/
    eta_taxpayer_activity_code_id = fields.Many2one(
        string='Taxpayer Activity Code',
        comodel_name='eta.activity.types',
        domain="[('eta_company_branch_id', '=', eta_company_branch_id)]",
        ondelete='restrict',
        help='Tax activity code of the business issuing the document \
            representing the activity that caused it to be issued.[9478]'
    )

    eta_is_einvoice = fields.Boolean(related='journal_id.eta_is_einvoice', store=True)


    def action_invoice_sign_eta(self):
        self.env['eta.manage']._sign_eta(self.filtered(lambda move: move.eta_is_einvoice))

    def action_invoice_send_eta(self):
        # Send only 10 invoice
        self.env['eta.manage']._send_eta(self.filtered(lambda move: move.eta_is_einvoice)[:10])

    def action_invoice_get_state_pdf(self):

        self.env['eta.manage'].get_eta_document_state(self.filtered(lambda move: move.eta_is_einvoice))
        self.env['eta.manage'].get_eta_document_pdf(self.filtered(lambda move: move.eta_is_einvoice))

    def action_invoice_cancel_document(self):

        self.env['eta.manage'].eta_document_cancel(self.filtered(lambda move: move.eta_is_einvoice))

    '''def action_invoice_reset(self):
        current_moves = self.env.context.get('active_ids')
        if not current_moves:
            current_moves = [self.id]

        for invoice_id in current_moves:
            move = self.env['account.move'].browse(invoice_id)
            if move.eta_state in ['tosign','signed']:
                move.write({'eta_json_text': False, 'eta_state': 'draft', 'eta_sign': False})'''

    @api.onchange('team_id')
    def _onchange_field(self):
        eta_company_branch_id = self.team_id.eta_company_branch_id
        if eta_company_branch_id:
            self.eta_company_branch_id = self.team_id.eta_company_branch_id

    # For Odoo action like cancel/reset to draft

    def button_draft(self):
        for move in self:
            if move.eta_state in ['accepted', 'submitted']:
                raise ValidationError(_('Please get eta state first.'))
            elif move.eta_state != 'valid':
                move.write({'eta_json_text': False,
                           'eta_state': 'draft', 'eta_sign': False})

        super().button_draft()

    def write(self, vals):
        for move in self:
            new_state = vals.get('state')
            if move.eta_state == 'valid' and new_state and new_state != 'draft' and not self.env.user.has_group('account.group_account_manager'):
                raise ValidationError(
                    _('Can\'t edit valid document cancel first.'))

        return super().write(vals)

    def _post(self, soft=True):
        for move in self:
            if not move.eta_date_time_issued:
                move.eta_date_time_issued = datetime.now()
        return super()._post(soft)

    # Make DEbit Note
    # TODO
