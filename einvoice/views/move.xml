<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_form_eta" model="ir.ui.view">
        <field name="name">view.move.form.eta</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <field name="eta_state" widget="statusbar" statusbar_visible="signed,accepted,valid,invalid"/>
            </xpath>
            <!--<xpath expr="//button[@name='action_reverse']" position="after">
                <button name="action_debit_note" string='Add Debit Note'
                    type='object' groups="account.group_account_invoice"
                    attrs="{'invisible': ['|', '|', ('move_type', 'not in', ('out_invoice')), ('state', '!=', 'posted'), ('eta_invoice_uuid', '!=', False)]}"/>
            </xpath>-->
            <xpath expr="//button[@name='button_cancel']" position="replace">
                <button name="button_cancel" string="Cancel Entry" type="object" groups="account.group_account_invoice"
                        invisible="id == False or state != 'draft' or eta_state == 'valid'"/>
                <button name="%(action_move_cancel_eta_button)d" string="Cancel Entry (ETA)" type="action" groups="account.group_account_invoice"
                        invisible="id == False or state != 'draft' or eta_state != 'valid'"/>
            </xpath>
            <xpath expr="//button[@name='button_draft']" position="after">
                <button name="action_invoice_sign_eta" style="background-color: #5c5963;color: white;"
                        type="object" string="ETA Sign"
                        invisible="state != 'posted' or eta_state in ('tosign','signed','accepted','submitted','valid') or
                                    move_type not in ('out_invoice', 'out_refund')" groups="account.group_account_invoice"/>
                <field name="eta_sign" invisible='1'/>
                <button name="action_invoice_send_eta" style="background-color: #5c5963;color: white;" 
                        type="object" string="ETA Send"
                        invisible="state != 'posted' or eta_state != 'signed' or move_type not in ('out_invoice', 'out_refund')"
                        groups="account.group_account_invoice"/>
                <!--<button name="action_invoice_reset" style="background-color: #99979c;color: white;" 
                type="object" string="ETA Reset" attrs="{'invisible':['|', '|', ('state', '!=', 'posted'), 
                ('eta_state', 'not in', ('tosign','signed')), ('move_type', 'not in', ('out_invoice', 'out_refund'))]}" />
                <button name="action_invoice_cancel_document" style="background-color: red;color: white;" 
                type="object" string="ETA Cancel" attrs="{'invisible':['|', '|', ('state', '!=', 'posted'), 
                ('eta_state', '!=', 'valid'), ('move_type', 'not in', ('out_invoice', 'out_refund'))]}" />-->
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="ETA" invisible="move_type not in ('out_invoice', 'out_refund')">
                    <group>
                        <group>
                            <!--<field name="eta_lang" attrs="{'readonly': [('eta_state', 'not in', 
                            ('draft','invalid', 'valid','rejected'))]}"/>-->
                            <field name="eta_is_preproduction" invisible='1'/>
                            <!--<field name="eta_document_type_version" 
                            attrs="{'readonly': [('eta_is_preproduction', '=', False)]}"/>-->
                        </group>
                        <group>
                            <field name="eta_document_type" readonly="eta_state not in ('draft','invalid', 'rejected')"/>
                            <field name="eta_date_time_issued" readonly="eta_state not in ('draft','invalid', 'rejected')"/>
                            <field name="eta_company_branch_id" readonly="eta_state not in ('draft','invalid', 'rejected')"
                            options='{"no_open": True, "no_create": True}' />
                            <field name="eta_taxpayer_activity_code_id" readonly="eta_state not in ('draft','invalid', 'rejected')"
                            options='{"no_open": True, "no_create": True}'/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="eta_invoice_uuid"
                                   readonly="eta_state in ('accepted','submitted','valid','tocancel')"/>
                            <field name="eta_uuid"
                                   invisible="eta_state not in ('accepted','submitted','valid','tocancel','invalid')"/>
                            <button name="action_invoice_get_state_pdf" style="background-color: #5c5963;color: white; width: 125px;"
                            type="object"
                                    invisible="state != 'posted' or eta_state not in ('accepted','submitted') or
                                                move_type not in ('out_invoice', 'out_refund')"
                            string="Get State &amp; PDF"
                            groups="account.group_account_invoice" />
                            <field name="eta_doc_pdf_name" invisible="1" />
                            <field name="eta_doc_pdf" filename="eta_doc_pdf_name" invisible="eta_doc_pdf == False"/>
                            <field name="eta_doc_pdf_ar" filename="eta_doc_pdf_name" eta_doc_pdf_ar="eta_doc_pdf_ar == False"/>
                        </group>
                        <group>
                            <field name="eta_reject_reason" invisible="eta_state != 'rejected'"/>
                            <field name="eta_invalid_reason" invisible="eta_state != 'invalid'"/>
                            <field name="eta_cancel_reason" readonly="eta_state == 'tocancel'"
                                   invisible="eta_state not in ('tocancel','valid')"/>
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
    <record id="view_move_tree_eta" model="ir.ui.view">
        <field name="name">view.move.tree.eta</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_invoice_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='status_in_payment']" position="after">
                <field name="eta_state" widget="badge" decoration-success="eta_state == 'valid'" 
                decoration-info="eta_state in ('accepted','submitted')" decoration-warning="eta_state == 'invalid'" 
                decoration-danger="eta_state == 'rejected'" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="move_sign_eta" model="ir.actions.server">
        <field name="name">ETA Sign</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.action_invoice_sign_eta()
        </field>
    </record>
    <record id="move_send_eta" model="ir.actions.server">
        <field name="name">ETA Send</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.action_invoice_send_eta()
        </field>
    </record>
    <record id="move_get_eta_state" model="ir.actions.server">
        <field name="name">ETA Get State &amp; PDF</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.action_invoice_get_state_pdf()
        </field>
    </record>
    <record id="view_move_search_eta" model="ir.ui.view">
        <field name="name">view.move.search.eta</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='date']" position="after">
                <field name="eta_uuid"/>
            </xpath>
            <xpath expr="//filter[@name='due_date']" position="after">
                <separator/>
                <filter name="tosign" string="To Sign" domain="[('eta_state','=','tosign')]"/>
                <filter name="valid" string="Valid" domain="[('eta_state','=','valid')]"/>
                <filter name="invalid" string="Invalid" domain="[('eta_state','=','invalid')]"/>
            </xpath>
            <xpath expr="//filter[@name='invoicedate']" position="after">
                <separator/>
                <filter string="ETA Status" name="eta_state" context="{'group_by': 'eta_state'}"/>
            </xpath>
        </field>
    </record>
    <!--<record id="move_rest_eta" model="ir.actions.server">
        <field name="name">ETA Reset</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        action = model.action_invoice_reset()
        </field>
    </record>-->
</odoo>