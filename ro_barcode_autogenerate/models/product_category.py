from odoo import models, fields, api, _

class ProductCategory(models.Model):
    
    _inherit = "product.category"
    
    ro_prefix = fields.Char(string='Prefix')
    ro_zeros_count = fields.Integer(string='Zeros Count', default=5)
    
    barcode_sequence_id = fields.Many2one(
            'ir.sequence', 
            string='Barcode Sequence',
            help='Sequence used to generate barcodes for products in this category'
        )

    @api.model_create_multi
    def create(self, vals_list):
        categories = super().create(vals_list)
        for category in categories:
            if not category.barcode_sequence_id:
                category._create_barcode_sequence()
        return categories

    def _create_barcode_sequence(self):
        sequence_vals = {
            'name': f'Barcode - {self.name}',
            'code': f'product.barcode.{self.id}',
            'prefix': '',  
            'padding': self.ro_zeros_count,
            'number_next_actual': 1,
            'implementation': 'standard',
            'company_id': self.env.company.id,
        }
        sequence = self.env['ir.sequence'].create(sequence_vals)
        self.barcode_sequence_id = sequence.id

    
    def write(self, vals):
        data = {}
        # if 'ro_prefix' in vals:
        #     data['prefix'] = vals.get('ro_prefix', '')
                
        if 'ro_zeros_count' in vals:
            data['padding'] = vals.get('ro_zeros_count', 0)
            
        for category in self:
            if category.barcode_sequence_id:
                category.barcode_sequence_id.write(data)
        
        return super(ProductCategory, self).write(vals)
    
    