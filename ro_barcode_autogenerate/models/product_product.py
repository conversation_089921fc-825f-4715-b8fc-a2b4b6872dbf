from odoo import models, fields, api, _

class ProductProduct(models.Model):
    
    _inherit = "product.product"
    
    def write(self, vals):
        if 'categ_id' in vals and not vals.get('barcode'):
            prev_category = self.categ_id
            category = self.env['product.category'].browse(vals['categ_id'])
            current_categ = category
            category_names = []
            while (current_categ.parent_id):
                category_names.append(current_categ.name)
                current_categ = current_categ.parent_id
            category_names.append(current_categ.name)
            category_names.reverse()
            category_name = "".join(category_names).replace(" ", "")
            if category:
                if category.barcode_sequence_id:
                    barcode_seq = category.barcode_sequence_id.next_by_id()
                    prev_category.barcode_sequence_id.return_to_pervious_state()
                else:
                    category._create_barcode_sequence()
                    barcode_seq = category.barcode_sequence_id.next_by_id()
                
                # barcode_seq = category.barcode_sequence_id.next_by_id()
                barcode = f"{category_name}{barcode_seq}"
                vals['barcode'] = barcode
        return super(ProductProduct, self).write(vals)
    
    
    @api.model
    def create(self, vals):
        if 'categ_id' in vals and not vals.get('barcode'):
            category = self.env['product.category'].browse(vals['categ_id'])
            current_categ = category
            category_names = []
            while (current_categ.parent_id):
                category_names.append(current_categ.name)
                current_categ = current_categ.parent_id
            category_names.append(current_categ.name)
            category_names.reverse()
            category_name = "".join(category_names).replace(" ", "")
            if category:
                if category.barcode_sequence_id:
                    barcode_seq = category.barcode_sequence_id.next_by_id()
                else:
                    category._create_barcode_sequence()
                    barcode_seq = category.barcode_sequence_id.next_by_id()
                # barcode_seq = category.barcode_sequence_id.next_by_id()
                barcode = f"{category_name}{barcode_seq}"
                vals['barcode'] = barcode
        
        return super(ProductProduct, self).create(vals)