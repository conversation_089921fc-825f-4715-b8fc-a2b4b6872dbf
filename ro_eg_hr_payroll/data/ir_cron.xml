<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

    <record model="ir.cron" id="employee_age_cron">
        <field name="name">calculate employee age</field>
        <field name="model_id" ref="hr.model_hr_employee"/>
        <field name="state">code</field>
        <field name="code">model._cron_employee_age()</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
    </record>


    <record model="ir.cron" id="employee_exp_cron">
        <field name="name">calculate employee experience</field>
        <field name="model_id" ref="hr.model_hr_employee"/>
        <field name="state">code</field>
        <field name="code">model._cron_employee_exp()</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
    </record>

    </data>
</odoo>