<?xml version="1.0" encoding="utf-8"?>


<odoo>
    <data>
        <record id="view_other_alw_form" model="ir.ui.view">
            <field name="name">view.other.alw.form</field>
            <field name="model">hr.alw</field>
            <field name="arch" type="xml">
                <form string="">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="amount"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>


        <record id="view_other_alw_tree" model="ir.ui.view">
            <field name="name">view.other.alw.tree</field>
            <field name="model">hr.alw</field>
            <field name="arch" type="xml">
                <list string="">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="amount"/>
                </list>
            </field>
        </record>

        <record id="view_action_other_alw" model="ir.actions.act_window">
            <field name="name">Employee Allowances</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">hr.alw</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    <!-- Add Text Here -->
                </p>
                <p>
                    <!-- More details about what a user can do with this object will be OK -->
                </p>
            </field>
        </record>


        <menuitem
                action="view_action_other_alw"
                id="menu_hr_alw"
                parent="hr.menu_human_resources_configuration"
                sequence="100"
                groups="hr.group_hr_manager"/>

    </data>
    <data>
        <record id="hr_contract_form_egypt_inherit" model="ir.ui.view">
            <field name="name">hr.contract.view.form.egypt.inherit</field>
            <field name="model">hr.contract</field>
            <field name="inherit_id" ref="hr_contract.hr_contract_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook/page[@name='information']" position="after">
                    <page name="insurance_info" string="Insurance Information">
                        <group>
                            <group>
                                <field name="insurance_job_position" />
                                
                                <field name="card_type" />
                                <field name="has_insurance_booklet" />
                                <field name="no_booklet_reason" invisible="has_insurance_booklet != 'no_booklet'" />
                                
                                <field name="sin_exist"/>
                                <field name="sin_no" invisible="sin_exist == False"/>
                                <field name="sin_date" invisible="sin_exist == False"/>
                                <field name="sin_end_date"
                                       invisible="sin_exist == False"/>
                            </group>
                            <group>
                                <!--<field name="experience"/>-->
                                <field name="mi_exist"/>
                                <field name="mi_no" invisible="mi_exist == False"/>
                                <field name="mi_date" invisible="mi_exist == False"/>
                            </group>
                        </group>
                    </page>
                   <page name="salary_items" string="Salary Items" groups="hr.group_hr_manager">
                        <group>
                            <group string="Insurance Items">
                                <field name="basic_salary"/>
                                <field name="variable_salary"/>
                                <field name="payment_type"/>
                            </group>
                            <group string="Allowances">
                                <field name="allowances"/>

                            </group>

                        </group>
                        <group string="Other Allowances">
                            <field name="other_alw_ids" nolabel="1">
                                <list string="Ohter allowances" editable="bottom">
                                    <field name="allowance_id" widget="selection"/>
                                    <field name="code"/>
                                    <field name="amount"/>
                                </list>
                            </field>
                        </group>

                    </page>
                </xpath>

            </field>
        </record>


    </data>
</odoo>