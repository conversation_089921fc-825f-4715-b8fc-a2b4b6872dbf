<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="hr_employee_name_form_view" model="ir.ui.view">
            <field name="name">hr.employee.name.form.view</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="placeholder">Emp. Name (English)</attribute>
                </xpath>
                <xpath expr="//field[@name='name']/.." position="after">
                    <h1 class="d-flex">
                        <field name="arabic_name" placeholder="Emp. Name (Arabic)" required="True" context="{'lang': 'ar_001'}"/>
                    </h1>
                </xpath>

                <xpath expr="//field[@name='identification_id']" position="after">
                    <field name="mother_name" />
                </xpath>

                <xpath expr="//field[@name='department_id']" position="after">
                    <field name="grade" />
                </xpath>

            </field>
        </record>

        <record id="hr_employee_name_kanban_view" model="ir.ui.view">
            <field name="name">hr.employee.name.kanban.view</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.hr_kanban_view_employees"/>
            <field name="arch" type="xml">
                <xpath expr="//main[hasclass('ms-2')]/div[1]" position="before">
                    <div>
                        <span t-if="record.arabic_name" class="o_kanban_record_subtitle">
                            <field name="arabic_name"/>
                        </span>
                    </div>
                </xpath>
            </field>
        </record>

    </data>
</odoo>