# Sale Order Admin Approval Level

This module adds an admin approval workflow to sale orders in Odoo.

## Features

- **New State**: Adds an "Admin Approved" state to sale orders
- **Admin Approve Button**: Adds an "ADMIN APPROVE" button visible only to users with admin approval rights
- **Controlled Workflow**: Sale orders must be admin approved before they can be confirmed
- **Security Group**: Creates a dedicated security group for admin approval permissions

## How it Works

1. **Draft State**: Sale orders start in draft state as usual
2. **Admin Approval**: Users with admin approval rights can click "ADMIN APPROVE" button
3. **Admin Approved State**: Sale order moves to "Admin Approved" state
4. **Confirmation**: Only after admin approval, the "CONFIRM" button becomes available
5. **Sale State**: Sale order can then be confirmed and moves to sale state

## Installation

1. Install the module through Odoo Apps
2. Assign users to the "Sale Admin Approval" group who should have admin approval rights

## Configuration

### Assigning Admin Approval Rights

1. Go to Settings > Users & Companies > Users
2. Select the user who should have admin approval rights
3. In the user form, go to the "Access Rights" tab
4. Find "Sales" section and check "Sale Admin Approval"

### Usage

1. Create a sale order as usual
2. In draft state, users with admin approval rights will see an "ADMIN APPROVE" button
3. Click "ADMIN APPROVE" to move the order to "Admin Approved" state
4. The "CONFIRM" button will then be available to confirm the sale order

## States

- **Draft**: Initial state, admin approval required
- **Admin Approved**: Approved by admin, ready for confirmation
- **Sale**: Confirmed sale order
- **Done**: Completed sale order
- **Cancelled**: Cancelled sale order

## Security

- Only users in the "Sale Admin Approval" group can see and use the "ADMIN APPROVE" button
- The module prevents confirmation of sale orders that haven't been admin approved
- Proper access controls ensure workflow integrity

## Technical Details

- Extends `sale.order` model
- Adds new state to the existing state selection
- Overrides `action_confirm` method to handle admin approval workflow
- Creates security group `ro_admin_approval_level.group_sale_admin_approval`
- Modifies sale order form and tree views
