# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from odoo.addons.sale.models.sale_order import SaleOrder


def _confirmation_error_message(self):
        """ Return whether order can be confirmed or not if not then returm error message. """
        self.ensure_one()
        if self.state not in {'draft', 'sent','admin_approved'}:
            return _("Some orders are not in a state requiring confirmation.")
        if self.state not in {'draft', 'sent','admin_approved'}:
            return _("Some orders are not in a state requiring confirmation.")
        if not self.partner_id:
            return _("You must first select a partner on these orders.")
        if not self.partner_id.vat:
            return _("You must first select a partner on these orders.")
        if any(
            not line.display_type
            and not line.is_downpayment
            and not line.product_id
            for line in self.order_line
        ):
            return _("A line on these orders missing a product, you cannot confirm it.")

        return False

SaleOrder._confirmation_error_message = _confirmation_error_message

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Override the state field to add the new admin_approved state
    state = fields.Selection(
        selection_add=[
            ('admin_approved', 'Admin Approved'),
        ],
        ondelete={'admin_approved': 'set default'}
    )

    def action_admin_approve(self):
        """
        Method to approve the sale order by admin.
        This will move the state to 'admin_approved' and allow the confirm button to show.
        """
        for order in self:
            if order.state != 'draft':
                raise UserError(_('Only draft orders can be admin approved.'))
            
            # Check if user has the right to admin approve
            if not self.env.user.has_group('ro_admin_approval_level.group_sale_admin_approval'):
                raise UserError(_('You do not have permission to admin approve sale orders.'))
            
            order.write({'state': 'admin_approved'})
            
            # Log the approval in the chatter
            order.message_post(
                body=_('Sale Order has been admin approved by %s') % self.env.user.name,
                message_type='notification'
            )
        
        return True

    def action_confirm(self):
        """
        Override the confirm method to allow confirmation from admin_approved state
        """
        for order in self:
            if order.state not in ['draft', 'sent', 'admin_approved']:
                continue
        
        return super(SaleOrder, self).action_confirm()

    def action_draft(self):
        """
        Override to handle the admin_approved state when going back to draft
        """
        orders = self.filtered(lambda s: s.state in ['cancel', 'admin_approved'])
        orders.write({'state': 'draft'})
        
        # Call super for other states
        other_orders = self - orders
        if other_orders:
            return super(SaleOrder, other_orders).action_draft()
        
        return True
