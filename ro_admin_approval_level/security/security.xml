<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <record id="group_sale_admin_approval" model="res.groups">
            <field name="name">Sale Admin Approval</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="comment">Users in this group can admin approve sale orders</field>
        </record>
    </data>
</odoo>
