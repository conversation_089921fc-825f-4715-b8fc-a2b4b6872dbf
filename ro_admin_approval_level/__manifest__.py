{
	'name': 'Sale Order Admin Approval Level',
	'author': 'Roayatec',
	'description': """
	Sale Order Admin Approval Workflow

	This module adds an admin approval workflow to sale orders:
	- Adds a new 'Admin Approved' state to sale orders
	- Adds an 'ADMIN APPROVE' button visible only to specific user groups
	- When clicked, moves the sale order to admin approved state
	- Shows the CONFIRM button only after admin approval
	 """,
	'website':'www.roayatec.com',
	'depends': ['base', 'sale', 'sale_management'],
	'license': 'OPL-1',
	'installable': True,
	'application': False,
	'auto-install': False,
	'data': [
		'security/security.xml',
		'security/ir.model.access.csv',
		'views/sale_order_views.xml',
	]
}
