<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <record id="view_order_form_admin_approval" model="ir.ui.view">
            <field name="name">sale.order.form.admin.approval</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                
                <xpath expr="//field[@name='state']" position="attributes">
                    <attribute name="statusbar_visible">draft,sent,admin_approved,sale,done</attribute>
                </xpath>
                
                <xpath expr="//button[@name='action_confirm']" position="before">
                    <button name="action_admin_approve" 
                            string="ADMIN APPROVE" 
                            type="object" 
                            class="btn-primary"
                            groups="ro_admin_approval_level.group_sale_admin_approval"
                            invisible="state != 'draft'" />
                </xpath>

                <xpath expr="//button[@name='action_cancel']" position="attributes">
                    <attribute name="invisible">state not in ['draft', 'sent', 'sale', 'admin_approved'] or not id or locked</attribute>
                </xpath>


                <xpath expr="//button[@name='action_quotation_send']" position="attributes">
                    <attribute name="invisible">state not in ['draft', 'sale', 'admin_approved']</attribute>
                </xpath>
                
                <xpath expr="(//button[@name='action_confirm'])[2]" position="attributes">
                    <attribute name="invisible">state not in ['admin_approved'] or order_line == []</attribute>
                </xpath>
                
            </field>
        </record>

        <record id="view_quotation_tree_admin_approval" model="ir.ui.view">
            <field name="name">sale.order.list.admin.approval</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_quotation_tree"/>
            <field name="arch" type="xml">
                
                <xpath expr="//list" position="attributes">
                    <attribute name="decoration-info">state == 'admin_approved'</attribute>
                </xpath>
                
            </field>
        </record>

        <record id="view_order_tree_admin_approval" model="ir.ui.view">
            <field name="name">sale.order.list.sales.admin.approval</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_tree"/>
            <field name="arch" type="xml">
                
                <xpath expr="//list" position="attributes">
                    <attribute name="decoration-info">state == 'admin_approved'</attribute>
                </xpath>
                
            </field>
        </record>

    </data>
</odoo>
