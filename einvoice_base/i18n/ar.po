# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* einvoice_base
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-06 23:51+0000\n"
"PO-Revision-Date: 2022-05-06 23:51+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: einvoice_base
#: model_terms:ir.ui.view,arch_db:einvoice_base.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Api Base Url</span>"
msgstr ""

#. module: einvoice_base
#: model_terms:ir.ui.view,arch_db:einvoice_base.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Client ID</span>"
msgstr ""

#. module: einvoice_base
#: model_terms:ir.ui.view,arch_db:einvoice_base.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Client Secret</span>"
msgstr ""

#. module: einvoice_base
#: model_terms:ir.ui.view,arch_db:einvoice_base.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Id Srv Base Url</span>"
msgstr ""

#. module: einvoice_base
#: model_terms:ir.ui.view,arch_db:einvoice_base.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Preproduction</span>"
msgstr ""

#. module: einvoice_base
#: model_terms:ir.ui.view,arch_db:einvoice_base.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Type Person Minimum</span>"
msgstr ""

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_res_company__eta_api_base_url
#: model:ir.model.fields,field_description:einvoice_base.field_res_config_settings__eta_api_base_url
msgid "Api Base Url"
msgstr ""

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_res_company__eta_client_id
#: model:ir.model.fields,field_description:einvoice_base.field_res_config_settings__eta_client_id
msgid "Client ID"
msgstr ""

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_res_company__eta_client_secret
#: model:ir.model.fields,field_description:einvoice_base.field_res_config_settings__eta_client_secret
msgid "Client Secret"
msgstr ""

#. module: einvoice_base
#: model:ir.model,name:einvoice_base.model_res_company
msgid "Companies"
msgstr "شركات"

#. module: einvoice_base
#: model:ir.model,name:einvoice_base.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الاعدادات"

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_ir_http__display_name
#: model:ir.model.fields,field_description:einvoice_base.field_mail_message__display_name
#: model:ir.model.fields,field_description:einvoice_base.field_res_company__display_name
#: model:ir.model.fields,field_description:einvoice_base.field_res_config_settings__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: einvoice_base
#: model:mail.channel,name:einvoice_base.channel_eta
msgid "ETA"
msgstr ""

#. module: einvoice_base
#: model_terms:ir.ui.view,arch_db:einvoice_base.res_config_settings_view_form
msgid "Electronic Invoice"
msgstr "الفاتورة الإلكترونية"

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_mail_mail__eta_message
#: model:ir.model.fields,field_description:einvoice_base.field_mail_message__eta_message
msgid "Eta Message"
msgstr ""

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_mail_mail__eta_message_done
#: model:ir.model.fields,field_description:einvoice_base.field_mail_message__eta_message_done
msgid "Eta Message Done"
msgstr ""

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_res_company__eta_generated_access_token
msgid "Generated Access Token"
msgstr ""

#. module: einvoice_base
#: model:ir.model,name:einvoice_base.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_ir_http__id
#: model:ir.model.fields,field_description:einvoice_base.field_mail_message__id
#: model:ir.model.fields,field_description:einvoice_base.field_res_company__id
#: model:ir.model.fields,field_description:einvoice_base.field_res_config_settings__id
msgid "ID"
msgstr "المُعرف"

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_res_company__eta_id_srv_base_url
#: model:ir.model.fields,field_description:einvoice_base.field_res_config_settings__eta_id_srv_base_url
msgid "Id Srv Base Url"
msgstr ""

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_ir_http____last_update
#: model:ir.model.fields,field_description:einvoice_base.field_mail_message____last_update
#: model:ir.model.fields,field_description:einvoice_base.field_res_company____last_update
#: model:ir.model.fields,field_description:einvoice_base.field_res_config_settings____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: einvoice_base
#: model:ir.model,name:einvoice_base.model_mail_message
msgid "Message"
msgstr "الرسالة"

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_res_company__eta_is_preproduction
#: model:ir.model.fields,field_description:einvoice_base.field_res_config_settings__eta_is_preproduction
msgid "Preproduction"
msgstr ""

#. module: einvoice_base
#: model:ir.model.fields,field_description:einvoice_base.field_res_company__eta_type_person_minimum
#: model:ir.model.fields,field_description:einvoice_base.field_res_config_settings__eta_type_person_minimum
msgid "Type Person Minimum"
msgstr ""