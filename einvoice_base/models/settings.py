# -*- coding: utf-8 -*-

from odoo import api, fields, models, _

class ResCompany(models.Model):
    _inherit = 'res.company'

    eta_is_preproduction = fields.Boolean(string='Preproduction')
    eta_api_base_url = fields.Char(string='Api Base Url', default="https://api.invoicing.eta.gov.eg")
    eta_id_srv_base_url = fields.Char(string='Id Srv Base Url', default="https://id.eta.gov.eg")
    eta_client_id = fields.Char(string='Client ID')
    eta_client_secret = fields.Char(string='Client Secret')
    eta_generated_access_token= fields.Char(string='Generated Access Token', readonly=True)
    eta_type_person_minimum = fields.Float(string='Type Person Minimum', default=50000)
    
    # For Setting Expire Token
    eta_token_timeout = fields.Datetime(string='Token Timeout')

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    eta_is_preproduction = fields.Boolean(related='company_id.eta_is_preproduction', readonly=False)
    eta_api_base_url = fields.Char(related='company_id.eta_api_base_url', readonly=False)
    eta_id_srv_base_url = fields.Char(related='company_id.eta_id_srv_base_url', readonly=False)
    eta_client_id = fields.Char(related='company_id.eta_client_id', readonly=False)
    eta_client_secret = fields.Char(related='company_id.eta_client_secret', readonly=False)
    eta_type_person_minimum = fields.Float( related='company_id.eta_type_person_minimum', readonly=False)