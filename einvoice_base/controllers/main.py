# -*- coding: utf-8 -*-

import json

from odoo import http, SUPERUSER_ID
from odoo.http import request


class ET<PERSON>ontroller(http.Controller):

    # Get notification from eta directly
    @http.route('/notifications/documents', type='json', auth="api_key", methods=['PUT'])
    def get_notification(self, **kwargs):

        channel = request.env.ref('einvoice_base.channel_eta')
        odoo_bot_partner = request.env.ref('base.partner_root')
        message = 'To use eta portal direct notification type please contact R<PERSON>ya (roayadm.com)'
        channel.message_post(
            body=message, subtype_xmlid='mail.mt_comment', author_id=odoo_bot_partner.id)

        """ channel = request.env.ref('einvoice_base.channel_eta')
        odoo_bot_partner = request.env.ref('base.partner_root')

        for mess in kwargs.get('Message'):
            message = '''
                Message id: <b>{}</b>,<br/>\n
                Type: <b>{}</b>,
                UUId: <b>{}</b>,
                InternalId: <b>{}</b>,
                Status: <b>{}</b>
                '''.format(kwargs.get('DeliveryId'), mess.get('Type'), mess.get('UUId'), mess.get('InternalId'),
                           mess.get('Status'))

            channel.message_post(
                body=message, subtype_xmlid='mail.mt_comment', author_id=odoo_bot_partner.id,
                eta_message=True) """

        return True

    @http.route('/Ping', type='json', auth="public", methods=['PUT'])
    def ping_return(self, **kwargs):

        data = {
            "rin": kwargs.get('Rin')
        }

        return data

    @http.route('/getdocuments/<int:company_id>', type='http', auth="api_key", methods=['GET'])
    def getdocs(self, company_id=1, **kwargs):

        domain = [('eta_json_text', '!=', False), ('eta_state',
                                                   '=', 'tosign'), ('company_id', '=', company_id)]

        moves = request.env['account.move'].search(
            domain, limit=10)

        invoices = []
        for move in moves:
            eta_json_text = move.eta_json_text
            eta_json_text = eta_json_text.replace("'", '"')
            invoices.append(json.loads(eta_json_text))

        data = json.dumps({'documents': invoices},
                          ensure_ascii=False).encode('utf8')

        return data

    @http.route('/setdocuments/<int:company_id>', type='json', auth="api_key", methods=['PUT'], csrf=False)
    def setdocs(self, company_id=1, **kwargs):
        try:
            signed_count = 0
            if kwargs:
                signatures = kwargs.get('signatures')
                for sign in signatures:
                    signed_count += 1

                    move_obj = request.env['account.move'].search(
                        [('name', '=', sign.get('InternalID')), ('company_id', '=', company_id)])

                    if move_obj:
                        move_obj.write(
                            {'eta_sign': sign.get('Sign'), 'eta_state': 'signed'})
            if signed_count > 0:
                state = {'code': 200, 'status': 'OK', 'signed': signed_count}
            else:
                state = {'code': 200, 'status': 'No Invoices Found', 'signed': 0}

        except:
            state = {'code': 400, 'status': 'Bad Request', 'signed': 0}

        return state

    # For companies with old eta and not have multi-company
    @http.route('/getdocuments', type='http', auth="api_key", methods=['GET'])
    def getdocsone(self, **kwargs):

        domain = [('eta_json_text', '!=', False), ('eta_state', '=', 'tosign')]

        moves = request.env['account.move'].search(
            domain, limit=10)

        invoices = []
        for move in moves:
            eta_json_text = move.eta_json_text
            eta_json_text = eta_json_text.replace("'", '"')
            invoices.append(json.loads(eta_json_text))

        data = json.dumps({'documents': invoices},
                          ensure_ascii=False).encode('utf8')

        return data

    @http.route('/setdocuments', type='json', auth="api_key", methods=['PUT'], csrf=False)
    def setdocsone(self, **kwargs):
        try:
            signed_count = 0
            if kwargs:
                signatures = kwargs.get('signatures')
                for sign in signatures:
                    signed_count += 1

                    move_obj = request.env['account.move'].search(
                        [('name', '=', sign.get('InternalID'))])

                    if move_obj:
                        move_obj.write(
                            {'eta_sign': sign.get('Sign'), 'eta_state': 'signed'})
            if signed_count > 0:
                state = {'code': 200, 'status': 'OK', 'signed': signed_count}
            else:
                state = {'code': 200, 'status': 'No Invoices Found', 'signed': 0}

        except:
            state = {'code': 400, 'status': 'Bad Request', 'signed': 0}

        return state


    
