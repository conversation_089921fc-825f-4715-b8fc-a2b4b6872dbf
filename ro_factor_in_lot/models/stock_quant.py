# from odoo import models, fields, api

# class StockQuant(models.Model):
#     _inherit = "stock.quant"

#     ro_meter = fields.Float(string="Meter", compute="_compute_ro_meter", store=True)

#     @api.depends('quantity', 'product_id.ro_factor', 'product_id.ro_has_factor')
#     def _compute_ro_meter(self):
#         for rec in self:
#             if rec.product_id and rec.product_id.ro_has_factor and rec.product_id.ro_factor:
#                 rec.ro_meter = rec.quantity * rec.product_id.ro_factor
#             else:
#                 rec.ro_meter = 0.0
                
from odoo import models, fields, api

class StockQuant(models.Model):
    _inherit = "stock.quant"

    ro_meter = fields.Float(string="Meter", compute="_compute_ro_meter", store=True)

    @api.depends('quantity', 'product_id.ro_factor', 'product_id.ro_has_factor')
    def _compute_ro_meter(self):
        for rec in self:
            if rec.product_id and rec.product_id.ro_has_factor and rec.product_id.ro_factor:
                rec.ro_meter = rec.inventory_quantity_auto_apply * rec.product_id.ro_factor
            else:
                rec.ro_meter = 0.0