from odoo import models, fields, api


class StockLot(models.Model):
    _inherit = "stock.lot"
    
    ro_height = fields.Float(string="Height", related="product_id.ro_height")
    ro_width = fields.Float(string="Width", related="product_id.ro_width")
    ro_factor = fields.Float(string="Factor", related="product_id.ro_factor")
    ro_meter = fields.Float(string="Meter", compute="_compute_ro_meter")
    
    @api.depends('product_qty', 'ro_factor')
    def _compute_ro_meter(self):
        for rec in self:
            rec.ro_meter = rec.product_qty * rec.ro_factor
